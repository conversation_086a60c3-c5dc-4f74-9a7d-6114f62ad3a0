(()=>{"use strict";var e={744:(e,t)=>{t.Z=(e,t)=>{const n=e.__vccOpts||e;for(const[e,s]of t)n[e]=s;return n}}},t={};function n(s){var o=t[s];if(void 0!==o)return o.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,n),r.exports}n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),(()=>{function e(e,t){const n=Object.create(null),s=e.split(",");for(let e=0;e<s.length;e++)n[s[e]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const t={},s=[],o=()=>{},r=()=>!1,i=/^on[^a-z]/,l=e=>i.test(e),a=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,p=(e,t)=>d.call(e,t),f=Array.isArray,h=e=>"[object Map]"===x(e),m=e=>"[object Set]"===x(e),g=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,b=e=>_(e)&&g(e.then)&&g(e.catch),w=Object.prototype.toString,x=e=>w.call(e),S=e=>x(e).slice(8,-1),C=e=>"[object Object]"===x(e),T=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,E=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),A=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},k=/-(\w)/g,O=A((e=>e.replace(k,((e,t)=>t?t.toUpperCase():"")))),I=/\B([A-Z])/g,M=A((e=>e.replace(I,"-$1").toLowerCase())),F=A((e=>e.charAt(0).toUpperCase()+e.slice(1))),N=A((e=>e?`on${F(e)}`:"")),$=(e,t)=>!Object.is(e,t),P=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},L=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},R=e=>{const t=parseFloat(e);return isNaN(t)?e:t},j=e=>{const t=v(e)?Number(e):NaN;return isNaN(t)?e:t};let W;const U=()=>W||(W="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n.g?n.g:{});function B(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],o=v(s)?z(s):B(s);if(o)for(const e in o)t[e]=o[e]}return t}return v(e)||_(e)?e:void 0}const H=/;(?![^(]*\))/g,D=/:([^]+)/,V=/\/\*[^]*?\*\//g;function z(e){const t={};return e.replace(V,"").split(H).forEach((e=>{if(e){const n=e.split(D);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function G(e){let t="";if(v(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const s=G(e[n]);s&&(t+=s+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const K=e("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function q(e){return!!e||""===e}const Z=e=>v(e)?e:null==e?"":f(e)||_(e)&&(e.toString===w||!g(e.toString))?JSON.stringify(e,J,2):String(e),J=(e,t)=>t&&t.__v_isRef?J(e,t.value):h(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:m(t)?{[`Set(${t.size})`]:[...t.values()]}:!_(t)||f(t)||C(t)?t:String(t);let X;class Q{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=X,!e&&X&&(this.index=(X.scopes||(X.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=X;try{return X=this,e()}finally{X=t}}}on(){X=this}off(){X=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}const Y=e=>{const t=new Set(e);return t.w=0,t.n=0,t},ee=e=>(e.w&oe)>0,te=e=>(e.n&oe)>0,ne=new WeakMap;let se=0,oe=1;const re=30;let ie;const le=Symbol(""),ae=Symbol("");class ce{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=X){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=ie,t=de;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=ie,ie=this,de=!0,oe=1<<++se,se<=re?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=oe})(this):ue(this),this.fn()}finally{se<=re&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let s=0;s<t.length;s++){const o=t[s];ee(o)&&!te(o)?o.delete(e):t[n++]=o,o.w&=~oe,o.n&=~oe}t.length=n}})(this),oe=1<<--se,ie=this.parent,de=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){ie===this?this.deferStop=!0:this.active&&(ue(this),this.onStop&&this.onStop(),this.active=!1)}}function ue(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let de=!0;const pe=[];function fe(){pe.push(de),de=!1}function he(){const e=pe.pop();de=void 0===e||e}function me(e,t,n){if(de&&ie){let t=ne.get(e);t||ne.set(e,t=new Map);let s=t.get(n);s||t.set(n,s=Y()),ge(s)}}function ge(e,t){let n=!1;se<=re?te(e)||(e.n|=oe,n=!ee(e)):n=!e.has(ie),n&&(e.add(ie),ie.deps.push(e))}function ve(e,t,n,s,o,r){const i=ne.get(e);if(!i)return;let l=[];if("clear"===t)l=[...i.values()];else if("length"===n&&f(e)){const e=Number(s);i.forEach(((t,n)=>{("length"===n||n>=e)&&l.push(t)}))}else switch(void 0!==n&&l.push(i.get(n)),t){case"add":f(e)?T(n)&&l.push(i.get("length")):(l.push(i.get(le)),h(e)&&l.push(i.get(ae)));break;case"delete":f(e)||(l.push(i.get(le)),h(e)&&l.push(i.get(ae)));break;case"set":h(e)&&l.push(i.get(le))}if(1===l.length)l[0]&&ye(l[0]);else{const e=[];for(const t of l)t&&e.push(...t);ye(Y(e))}}function ye(e,t){const n=f(e)?e:[...e];for(const e of n)e.computed&&_e(e);for(const e of n)e.computed||_e(e)}function _e(e,t){(e!==ie||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const be=e("__proto__,__v_isRef,__isVue"),we=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y)),xe=ke(),Se=ke(!1,!0),Ce=ke(!0),Te=Ee();function Ee(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=dt(this);for(let e=0,t=this.length;e<t;e++)me(n,0,e+"");const s=n[t](...e);return-1===s||!1===s?n[t](...e.map(dt)):s}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){fe();const n=dt(this)[t].apply(this,e);return he(),n}})),e}function Ae(e){const t=dt(this);return me(t,0,e),t.hasOwnProperty(e)}function ke(e=!1,t=!1){return function(n,s,o){if("__v_isReactive"===s)return!e;if("__v_isReadonly"===s)return e;if("__v_isShallow"===s)return t;if("__v_raw"===s&&o===(e?t?st:nt:t?tt:et).get(n))return n;const r=f(n);if(!e){if(r&&p(Te,s))return Reflect.get(Te,s,o);if("hasOwnProperty"===s)return Ae}const i=Reflect.get(n,s,o);return(y(s)?we.has(s):be(s))?i:(e||me(n,0,s),t?i:mt(i)?r&&T(s)?i:i.value:_(i)?e?rt(i):ot(i):i)}}function Oe(e=!1){return function(t,n,s,o){let r=t[n];if(at(r)&&mt(r)&&!mt(s))return!1;if(!e&&(ct(s)||at(s)||(r=dt(r),s=dt(s)),!f(t)&&mt(r)&&!mt(s)))return r.value=s,!0;const i=f(t)&&T(n)?Number(n)<t.length:p(t,n),l=Reflect.set(t,n,s,o);return t===dt(o)&&(i?$(s,r)&&ve(t,"set",n,s):ve(t,"add",n,s)),l}}const Ie={get:xe,set:Oe(),deleteProperty:function(e,t){const n=p(e,t),s=(e[t],Reflect.deleteProperty(e,t));return s&&n&&ve(e,"delete",t,void 0),s},has:function(e,t){const n=Reflect.has(e,t);return y(t)&&we.has(t)||me(e,0,t),n},ownKeys:function(e){return me(e,0,f(e)?"length":le),Reflect.ownKeys(e)}},Me={get:Ce,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},Fe=c({},Ie,{get:Se,set:Oe(!0)}),Ne=e=>e,$e=e=>Reflect.getPrototypeOf(e);function Pe(e,t,n=!1,s=!1){const o=dt(e=e.__v_raw),r=dt(t);n||(t!==r&&me(o,0,t),me(o,0,r));const{has:i}=$e(o),l=s?Ne:n?ht:ft;return i.call(o,t)?l(e.get(t)):i.call(o,r)?l(e.get(r)):void(e!==o&&e.get(t))}function Le(e,t=!1){const n=this.__v_raw,s=dt(n),o=dt(e);return t||(e!==o&&me(s,0,e),me(s,0,o)),e===o?n.has(e):n.has(e)||n.has(o)}function Re(e,t=!1){return e=e.__v_raw,!t&&me(dt(e),0,le),Reflect.get(e,"size",e)}function je(e){e=dt(e);const t=dt(this);return $e(t).has.call(t,e)||(t.add(e),ve(t,"add",e,e)),this}function We(e,t){t=dt(t);const n=dt(this),{has:s,get:o}=$e(n);let r=s.call(n,e);r||(e=dt(e),r=s.call(n,e));const i=o.call(n,e);return n.set(e,t),r?$(t,i)&&ve(n,"set",e,t):ve(n,"add",e,t),this}function Ue(e){const t=dt(this),{has:n,get:s}=$e(t);let o=n.call(t,e);o||(e=dt(e),o=n.call(t,e)),s&&s.call(t,e);const r=t.delete(e);return o&&ve(t,"delete",e,void 0),r}function Be(){const e=dt(this),t=0!==e.size,n=e.clear();return t&&ve(e,"clear",void 0,void 0),n}function He(e,t){return function(n,s){const o=this,r=o.__v_raw,i=dt(r),l=t?Ne:e?ht:ft;return!e&&me(i,0,le),r.forEach(((e,t)=>n.call(s,l(e),l(t),o)))}}function De(e,t,n){return function(...s){const o=this.__v_raw,r=dt(o),i=h(r),l="entries"===e||e===Symbol.iterator&&i,a="keys"===e&&i,c=o[e](...s),u=n?Ne:t?ht:ft;return!t&&me(r,0,a?ae:le),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:l?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Ve(e){return function(...t){return"delete"!==e&&this}}function ze(){const e={get(e){return Pe(this,e)},get size(){return Re(this)},has:Le,add:je,set:We,delete:Ue,clear:Be,forEach:He(!1,!1)},t={get(e){return Pe(this,e,!1,!0)},get size(){return Re(this)},has:Le,add:je,set:We,delete:Ue,clear:Be,forEach:He(!1,!0)},n={get(e){return Pe(this,e,!0)},get size(){return Re(this,!0)},has(e){return Le.call(this,e,!0)},add:Ve("add"),set:Ve("set"),delete:Ve("delete"),clear:Ve("clear"),forEach:He(!0,!1)},s={get(e){return Pe(this,e,!0,!0)},get size(){return Re(this,!0)},has(e){return Le.call(this,e,!0)},add:Ve("add"),set:Ve("set"),delete:Ve("delete"),clear:Ve("clear"),forEach:He(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((o=>{e[o]=De(o,!1,!1),n[o]=De(o,!0,!1),t[o]=De(o,!1,!0),s[o]=De(o,!0,!0)})),[e,n,t,s]}const[Ge,Ke,qe,Ze]=ze();function Je(e,t){const n=t?e?Ze:qe:e?Ke:Ge;return(t,s,o)=>"__v_isReactive"===s?!e:"__v_isReadonly"===s?e:"__v_raw"===s?t:Reflect.get(p(n,s)&&s in t?n:t,s,o)}const Xe={get:Je(!1,!1)},Qe={get:Je(!1,!0)},Ye={get:Je(!0,!1)},et=new WeakMap,tt=new WeakMap,nt=new WeakMap,st=new WeakMap;function ot(e){return at(e)?e:it(e,!1,Ie,Xe,et)}function rt(e){return it(e,!0,Me,Ye,nt)}function it(e,t,n,s,o){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const r=o.get(e);if(r)return r;const i=(l=e).__v_skip||!Object.isExtensible(l)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(S(l));var l;if(0===i)return e;const a=new Proxy(e,2===i?s:n);return o.set(e,a),a}function lt(e){return at(e)?lt(e.__v_raw):!(!e||!e.__v_isReactive)}function at(e){return!(!e||!e.__v_isReadonly)}function ct(e){return!(!e||!e.__v_isShallow)}function ut(e){return lt(e)||at(e)}function dt(e){const t=e&&e.__v_raw;return t?dt(t):e}function pt(e){return L(e,"__v_skip",!0),e}const ft=e=>_(e)?ot(e):e,ht=e=>_(e)?rt(e):e;function mt(e){return!(!e||!0!==e.__v_isRef)}const gt={get:(e,t,n)=>{return mt(s=Reflect.get(e,t,n))?s.value:s;var s},set:(e,t,n,s)=>{const o=e[t];return mt(o)&&!mt(n)?(o.value=n,!0):Reflect.set(e,t,n,s)}};function vt(e){return lt(e)?e:new Proxy(e,gt)}class yt{constructor(e,t,n,s){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new ce(e,(()=>{this._dirty||(this._dirty=!0,function(e,t){const n=(e=dt(e)).dep;n&&ye(n)}(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!s,this.__v_isReadonly=n}get value(){const e=dt(this);return t=e,de&&ie&&ge((t=dt(t)).dep||(t.dep=Y())),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value;var t}set value(e){this._setter(e)}}function _t(e,t,n,s){let o;try{o=s?e(...s):e()}catch(e){wt(e,t,n)}return o}function bt(e,t,n,s){if(g(e)){const o=_t(e,t,n,s);return o&&b(o)&&o.catch((e=>{wt(e,t,n)})),o}const o=[];for(let r=0;r<e.length;r++)o.push(bt(e[r],t,n,s));return o}function wt(e,t,n,s=!0){if(t&&t.vnode,t){let s=t.parent;const o=t.proxy,r=n;for(;s;){const t=s.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,o,r))return;s=s.parent}const i=t.appContext.config.errorHandler;if(i)return void _t(i,null,10,[e,o,r])}!function(e,t,n,s=!0){console.error(e)}(e,0,0,s)}let xt=!1,St=!1;const Ct=[];let Tt=0;const Et=[];let At=null,kt=0;const Ot=Promise.resolve();let It=null;function Mt(e){const t=It||Ot;return e?t.then(this?e.bind(this):e):t}function Ft(e){Ct.length&&Ct.includes(e,xt&&e.allowRecurse?Tt+1:Tt)||(null==e.id?Ct.push(e):Ct.splice(function(e){let t=Tt+1,n=Ct.length;for(;t<n;){const s=t+n>>>1;Lt(Ct[s])<e?t=s+1:n=s}return t}(e.id),0,e),Nt())}function Nt(){xt||St||(St=!0,It=Ot.then(jt))}function $t(e,t=(xt?Tt+1:0)){for(;t<Ct.length;t++){const e=Ct[t];e&&e.pre&&(Ct.splice(t,1),t--,e())}}function Pt(e){if(Et.length){const e=[...new Set(Et)];if(Et.length=0,At)return void At.push(...e);for(At=e,At.sort(((e,t)=>Lt(e)-Lt(t))),kt=0;kt<At.length;kt++)At[kt]();At=null,kt=0}}const Lt=e=>null==e.id?1/0:e.id,Rt=(e,t)=>{const n=Lt(e)-Lt(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function jt(e){St=!1,xt=!0,Ct.sort(Rt);try{for(Tt=0;Tt<Ct.length;Tt++){const e=Ct[Tt];e&&!1!==e.active&&_t(e,null,14)}}finally{Tt=0,Ct.length=0,Pt(),xt=!1,It=null,(Ct.length||Et.length)&&jt(e)}}function Wt(e,n,...s){if(e.isUnmounted)return;const o=e.vnode.props||t;let r=s;const i=n.startsWith("update:"),l=i&&n.slice(7);if(l&&l in o){const e=`${"modelValue"===l?"model":l}Modifiers`,{number:n,trim:i}=o[e]||t;i&&(r=s.map((e=>v(e)?e.trim():e))),n&&(r=s.map(R))}let a,c=o[a=N(n)]||o[a=N(O(n))];!c&&i&&(c=o[a=N(M(n))]),c&&bt(c,e,6,r);const u=o[a+"Once"];if(u){if(e.emitted){if(e.emitted[a])return}else e.emitted={};e.emitted[a]=!0,bt(u,e,6,r)}}function Ut(e,t,n=!1){const s=t.emitsCache,o=s.get(e);if(void 0!==o)return o;const r=e.emits;let i={},l=!1;if(!g(e)){const s=e=>{const n=Ut(e,t,!0);n&&(l=!0,c(i,n))};!n&&t.mixins.length&&t.mixins.forEach(s),e.extends&&s(e.extends),e.mixins&&e.mixins.forEach(s)}return r||l?(f(r)?r.forEach((e=>i[e]=null)):c(i,r),_(e)&&s.set(e,i),i):(_(e)&&s.set(e,null),null)}function Bt(e,t){return!(!e||!l(t))&&(t=t.slice(2).replace(/Once$/,""),p(e,t[0].toLowerCase()+t.slice(1))||p(e,M(t))||p(e,t))}let Ht=null,Dt=null;function Vt(e){const t=Ht;return Ht=e,Dt=e&&e.type.__scopeId||null,t}function zt(e){const{type:t,vnode:n,proxy:s,withProxy:o,props:r,propsOptions:[i],slots:l,attrs:c,emit:u,render:d,renderCache:p,data:f,setupState:h,ctx:m,inheritAttrs:g}=e;let v,y;const _=Vt(e);try{if(4&n.shapeFlag){const e=o||s;v=zs(d.call(e,e,p,r,h,f,m)),y=c}else{const e=t;v=zs(e.length>1?e(r,{attrs:c,slots:l,emit:u}):e(r,null)),y=t.props?c:Gt(c)}}catch(t){As.length=0,wt(t,e,1),v=Bs(Ts)}let b=v;if(y&&!1!==g){const e=Object.keys(y),{shapeFlag:t}=b;e.length&&7&t&&(i&&e.some(a)&&(y=Kt(y,i)),b=Hs(b,y))}return n.dirs&&(b=Hs(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),v=b,Vt(_),v}const Gt=e=>{let t;for(const n in e)("class"===n||"style"===n||l(n))&&((t||(t={}))[n]=e[n]);return t},Kt=(e,t)=>{const n={};for(const s in e)a(s)&&s.slice(9)in t||(n[s]=e[s]);return n};function qt(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let o=0;o<s.length;o++){const r=s[o];if(t[r]!==e[r]&&!Bt(n,r))return!0}return!1}const Zt={};function Jt(e,t,n){return Xt(e,t,n)}function Xt(e,n,{immediate:s,deep:r,flush:i,onTrack:l,onTrigger:a}=t){var c;const d=X===(null==(c=Xs)?void 0:c.scope)?Xs:null;let p,h,m=!1,v=!1;if(mt(e)?(p=()=>e.value,m=ct(e)):lt(e)?(p=()=>e,r=!0):f(e)?(v=!0,m=e.some((e=>lt(e)||ct(e))),p=()=>e.map((e=>mt(e)?e.value:lt(e)?en(e):g(e)?_t(e,d,2):void 0))):p=g(e)?n?()=>_t(e,d,2):()=>{if(!d||!d.isUnmounted)return h&&h(),bt(e,d,3,[_])}:o,n&&r){const e=p;p=()=>en(e())}let y,_=e=>{h=S.onStop=()=>{_t(e,d,4)}};if(lo){if(_=o,n?s&&bt(n,d,3,[p(),v?[]:void 0,_]):p(),"sync"!==i)return o;{const e=ho();y=e.__watcherHandles||(e.__watcherHandles=[])}}let b=v?new Array(e.length).fill(Zt):Zt;const w=()=>{if(S.active)if(n){const e=S.run();(r||m||(v?e.some(((e,t)=>$(e,b[t]))):$(e,b)))&&(h&&h(),bt(n,d,3,[e,b===Zt?void 0:v&&b[0]===Zt?[]:b,_]),b=e)}else S.run()};let x;w.allowRecurse=!!n,"sync"===i?x=w:"post"===i?x=()=>_s(w,d&&d.suspense):(w.pre=!0,d&&(w.id=d.uid),x=()=>Ft(w));const S=new ce(p,x);n?s?w():b=S.run():"post"===i?_s(S.run.bind(S),d&&d.suspense):S.run();const C=()=>{S.stop(),d&&d.scope&&u(d.scope.effects,S)};return y&&y.push(C),C}function Qt(e,t,n){const s=this.proxy,o=v(e)?e.includes(".")?Yt(s,e):()=>s[e]:e.bind(s,s);let r;g(t)?r=t:(r=t.handler,n=t);const i=Xs;no(this);const l=Xt(o,r.bind(s),n);return i?no(i):so(),l}function Yt(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function en(e,t){if(!_(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),mt(e))en(e.value,t);else if(f(e))for(let n=0;n<e.length;n++)en(e[n],t);else if(m(e)||h(e))e.forEach((e=>{en(e,t)}));else if(C(e))for(const n in e)en(e[n],t);return e}function tn(e,n){const s=Ht;if(null===s)return e;const o=uo(s)||s.proxy,r=e.dirs||(e.dirs=[]);for(let e=0;e<n.length;e++){let[s,i,l,a=t]=n[e];s&&(g(s)&&(s={mounted:s,updated:s}),s.deep&&en(i),r.push({dir:s,instance:o,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function nn(e,t,n,s){const o=e.dirs,r=t&&t.dirs;for(let i=0;i<o.length;i++){const l=o[i];r&&(l.oldValue=r[i].value);let a=l.dir[s];a&&(fe(),bt(a,n,8,[e.el,l,e,t]),he())}}const sn=[Function,Array],on={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:sn,onEnter:sn,onAfterEnter:sn,onEnterCancelled:sn,onBeforeLeave:sn,onLeave:sn,onAfterLeave:sn,onLeaveCancelled:sn,onBeforeAppear:sn,onAppear:sn,onAfterAppear:sn,onAppearCancelled:sn},rn={name:"BaseTransition",props:on,setup(e,{slots:t}){const n=Qs(),s=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Sn((()=>{e.isMounted=!0})),En((()=>{e.isUnmounting=!0})),e}();let o;return()=>{const r=t.default&&pn(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1){let e=!1;for(const t of r)if(t.type!==Ts){i=t,e=!0;break}}const l=dt(e),{mode:a}=l;if(s.isLeaving)return cn(i);const c=un(i);if(!c)return cn(i);const u=an(c,l,s,n);dn(c,u);const d=n.subTree,p=d&&un(d);let f=!1;const{getTransitionKey:h}=c.type;if(h){const e=h();void 0===o?o=e:e!==o&&(o=e,f=!0)}if(p&&p.type!==Ts&&(!Ls(c,p)||f)){const e=an(p,l,s,n);if(dn(p,e),"out-in"===a)return s.isLeaving=!0,e.afterLeave=()=>{s.isLeaving=!1,!1!==n.update.active&&n.update()},cn(i);"in-out"===a&&c.type!==Ts&&(e.delayLeave=(e,t,n)=>{ln(s,p)[String(p.key)]=p,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return i}}};function ln(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function an(e,t,n,s){const{appear:o,mode:r,persisted:i=!1,onBeforeEnter:l,onEnter:a,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:p,onAfterLeave:h,onLeaveCancelled:m,onBeforeAppear:g,onAppear:v,onAfterAppear:y,onAppearCancelled:_}=t,b=String(e.key),w=ln(n,e),x=(e,t)=>{e&&bt(e,s,9,t)},S=(e,t)=>{const n=t[1];x(e,t),f(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},C={mode:r,persisted:i,beforeEnter(t){let s=l;if(!n.isMounted){if(!o)return;s=g||l}t._leaveCb&&t._leaveCb(!0);const r=w[b];r&&Ls(e,r)&&r.el._leaveCb&&r.el._leaveCb(),x(s,[t])},enter(e){let t=a,s=c,r=u;if(!n.isMounted){if(!o)return;t=v||a,s=y||c,r=_||u}let i=!1;const l=e._enterCb=t=>{i||(i=!0,x(t?r:s,[e]),C.delayedLeave&&C.delayedLeave(),e._enterCb=void 0)};t?S(t,[e,l]):l()},leave(t,s){const o=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return s();x(d,[t]);let r=!1;const i=t._leaveCb=n=>{r||(r=!0,s(),x(n?m:h,[t]),t._leaveCb=void 0,w[o]===e&&delete w[o])};w[o]=e,p?S(p,[t,i]):i()},clone:e=>an(e,t,n,s)};return C}function cn(e){if(mn(e))return(e=Hs(e)).children=null,e}function un(e){return mn(e)?e.children?e.children[0]:void 0:e}function dn(e,t){6&e.shapeFlag&&e.component?dn(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function pn(e,t=!1,n){let s=[],o=0;for(let r=0;r<e.length;r++){let i=e[r];const l=null==n?i.key:String(n)+String(null!=i.key?i.key:r);i.type===Ss?(128&i.patchFlag&&o++,s=s.concat(pn(i.children,t,l))):(t||i.type!==Ts)&&s.push(null!=l?Hs(i,{key:l}):i)}if(o>1)for(let e=0;e<s.length;e++)s[e].patchFlag=-2;return s}function fn(e,t){return g(e)?(()=>c({name:e.name},t,{setup:e}))():e}const hn=e=>!!e.type.__asyncLoader,mn=e=>e.type.__isKeepAlive;function gn(e,t){yn(e,"a",t)}function vn(e,t){yn(e,"da",t)}function yn(e,t,n=Xs){const s=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(bn(t,s,n),n){let e=n.parent;for(;e&&e.parent;)mn(e.parent.vnode)&&_n(s,t,n,e),e=e.parent}}function _n(e,t,n,s){const o=bn(t,e,s,!0);An((()=>{u(s[t],o)}),n)}function bn(e,t,n=Xs,s=!1){if(n){const o=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...s)=>{if(n.isUnmounted)return;fe(),no(n);const o=bt(t,n,e,s);return so(),he(),o});return s?o.unshift(r):o.push(r),r}}RegExp,RegExp;const wn=e=>(t,n=Xs)=>(!lo||"sp"===e)&&bn(e,((...e)=>t(...e)),n),xn=wn("bm"),Sn=wn("m"),Cn=wn("bu"),Tn=wn("u"),En=wn("bum"),An=wn("um"),kn=wn("sp"),On=wn("rtg"),In=wn("rtc");function Mn(e,t=Xs){bn("ec",e,t)}const Fn="components";function Nn(e,t){return function(e,t,n=!0,s=!1){const o=Ht||Xs;if(o){const n=o.type;if(e===Fn){const e=function(e,t=!0){return g(e)?e.displayName||e.name:e.name||t&&e.__name}(n,!1);if(e&&(e===t||e===O(t)||e===F(O(t))))return n}const r=Pn(o[e]||n[e],t)||Pn(o.appContext[e],t);return!r&&s?n:r}}(Fn,e,!0,t)||e}const $n=Symbol.for("v-ndc");function Pn(e,t){return e&&(e[t]||e[O(t)]||e[F(O(t))])}function Ln(e,t,n,s){let o;const r=n&&n[s];if(f(e)||v(e)){o=new Array(e.length);for(let n=0,s=e.length;n<s;n++)o[n]=t(e[n],n,void 0,r&&r[n])}else if("number"==typeof e){o=new Array(e);for(let n=0;n<e;n++)o[n]=t(n+1,n,void 0,r&&r[n])}else if(_(e))if(e[Symbol.iterator])o=Array.from(e,((e,n)=>t(e,n,void 0,r&&r[n])));else{const n=Object.keys(e);o=new Array(n.length);for(let s=0,i=n.length;s<i;s++){const i=n[s];o[s]=t(e[i],i,s,r&&r[s])}}else o=[];return n&&(n[s]=o),o}const Rn=e=>e?oo(e)?uo(e)||e.proxy:Rn(e.parent):null,jn=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Rn(e.parent),$root:e=>Rn(e.root),$emit:e=>e.emit,$options:e=>zn(e),$forceUpdate:e=>e.f||(e.f=()=>Ft(e.update)),$nextTick:e=>e.n||(e.n=Mt.bind(e.proxy)),$watch:e=>Qt.bind(e)}),Wn=(e,n)=>e!==t&&!e.__isScriptSetup&&p(e,n),Un={get({_:e},n){const{ctx:s,setupState:o,data:r,props:i,accessCache:l,type:a,appContext:c}=e;let u;if("$"!==n[0]){const a=l[n];if(void 0!==a)switch(a){case 1:return o[n];case 2:return r[n];case 4:return s[n];case 3:return i[n]}else{if(Wn(o,n))return l[n]=1,o[n];if(r!==t&&p(r,n))return l[n]=2,r[n];if((u=e.propsOptions[0])&&p(u,n))return l[n]=3,i[n];if(s!==t&&p(s,n))return l[n]=4,s[n];Hn&&(l[n]=0)}}const d=jn[n];let f,h;return d?("$attrs"===n&&me(e,0,n),d(e)):(f=a.__cssModules)&&(f=f[n])?f:s!==t&&p(s,n)?(l[n]=4,s[n]):(h=c.config.globalProperties,p(h,n)?h[n]:void 0)},set({_:e},n,s){const{data:o,setupState:r,ctx:i}=e;return Wn(r,n)?(r[n]=s,!0):o!==t&&p(o,n)?(o[n]=s,!0):!(p(e.props,n)||"$"===n[0]&&n.slice(1)in e||(i[n]=s,0))},has({_:{data:e,setupState:n,accessCache:s,ctx:o,appContext:r,propsOptions:i}},l){let a;return!!s[l]||e!==t&&p(e,l)||Wn(n,l)||(a=i[0])&&p(a,l)||p(o,l)||p(jn,l)||p(r.config.globalProperties,l)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:p(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Bn(e){return f(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let Hn=!0;function Dn(e,t,n){bt(f(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function Vn(e,t,n,s){const o=s.includes(".")?Yt(n,s):()=>n[s];if(v(e)){const n=t[e];g(n)&&Jt(o,n)}else if(g(e))Jt(o,e.bind(n));else if(_(e))if(f(e))e.forEach((e=>Vn(e,t,n,s)));else{const s=g(e.handler)?e.handler.bind(n):t[e.handler];g(s)&&Jt(o,s,e)}}function zn(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:o,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,l=r.get(t);let a;return l?a=l:o.length||n||s?(a={},o.length&&o.forEach((e=>Gn(a,e,i,!0))),Gn(a,t,i)):a=t,_(t)&&r.set(t,a),a}function Gn(e,t,n,s=!1){const{mixins:o,extends:r}=t;r&&Gn(e,r,n,!0),o&&o.forEach((t=>Gn(e,t,n,!0)));for(const o in t)if(s&&"expose"===o);else{const s=Kn[o]||n&&n[o];e[o]=s?s(e[o],t[o]):t[o]}return e}const Kn={data:qn,props:Qn,emits:Qn,methods:Xn,computed:Xn,beforeCreate:Jn,created:Jn,beforeMount:Jn,mounted:Jn,beforeUpdate:Jn,updated:Jn,beforeDestroy:Jn,beforeUnmount:Jn,destroyed:Jn,unmounted:Jn,activated:Jn,deactivated:Jn,errorCaptured:Jn,serverPrefetch:Jn,components:Xn,directives:Xn,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const s in t)n[s]=Jn(e[s],t[s]);return n},provide:qn,inject:function(e,t){return Xn(Zn(e),Zn(t))}};function qn(e,t){return t?e?function(){return c(g(e)?e.call(this,this):e,g(t)?t.call(this,this):t)}:t:e}function Zn(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Jn(e,t){return e?[...new Set([].concat(e,t))]:t}function Xn(e,t){return e?c(Object.create(null),e,t):t}function Qn(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:c(Object.create(null),Bn(e),Bn(null!=t?t:{})):t}function Yn(){return{app:null,config:{isNativeTag:r,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let es=0;function ts(e,t){return function(n,s=null){g(n)||(n=c({},n)),null==s||_(s)||(s=null);const o=Yn(),r=new Set;let i=!1;const l=o.app={_uid:es++,_component:n,_props:s,_container:null,_context:o,_instance:null,version:mo,get config(){return o.config},set config(e){},use:(e,...t)=>(r.has(e)||(e&&g(e.install)?(r.add(e),e.install(l,...t)):g(e)&&(r.add(e),e(l,...t))),l),mixin:e=>(o.mixins.includes(e)||o.mixins.push(e),l),component:(e,t)=>t?(o.components[e]=t,l):o.components[e],directive:(e,t)=>t?(o.directives[e]=t,l):o.directives[e],mount(r,a,c){if(!i){const u=Bs(n,s);return u.appContext=o,a&&t?t(u,r):e(u,r,c),i=!0,l._container=r,r.__vue_app__=l,uo(u.component)||u.component.proxy}},unmount(){i&&(e(null,l._container),delete l._container.__vue_app__)},provide:(e,t)=>(o.provides[e]=t,l),runWithContext(e){ns=l;try{return e()}finally{ns=null}}};return l}}let ns=null;function ss(e,t,n=!1){const s=Xs||Ht;if(s||ns){const o=s?null==s.parent?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:ns._context.provides;if(o&&e in o)return o[e];if(arguments.length>1)return n&&g(t)?t.call(s&&s.proxy):t}}function os(e,n,s,o){const[r,i]=e.propsOptions;let l,a=!1;if(n)for(let t in n){if(E(t))continue;const c=n[t];let u;r&&p(r,u=O(t))?i&&i.includes(u)?(l||(l={}))[u]=c:s[u]=c:Bt(e.emitsOptions,t)||t in o&&c===o[t]||(o[t]=c,a=!0)}if(i){const n=dt(s),o=l||t;for(let t=0;t<i.length;t++){const l=i[t];s[l]=rs(r,n,l,o[l],e,!p(o,l))}}return a}function rs(e,t,n,s,o,r){const i=e[n];if(null!=i){const e=p(i,"default");if(e&&void 0===s){const e=i.default;if(i.type!==Function&&!i.skipFactory&&g(e)){const{propsDefaults:r}=o;n in r?s=r[n]:(no(o),s=r[n]=e.call(null,t),so())}else s=e}i[0]&&(r&&!e?s=!1:!i[1]||""!==s&&s!==M(n)||(s=!0))}return s}function is(e,n,o=!1){const r=n.propsCache,i=r.get(e);if(i)return i;const l=e.props,a={},u=[];let d=!1;if(!g(e)){const t=e=>{d=!0;const[t,s]=is(e,n,!0);c(a,t),s&&u.push(...s)};!o&&n.mixins.length&&n.mixins.forEach(t),e.extends&&t(e.extends),e.mixins&&e.mixins.forEach(t)}if(!l&&!d)return _(e)&&r.set(e,s),s;if(f(l))for(let e=0;e<l.length;e++){const n=O(l[e]);ls(n)&&(a[n]=t)}else if(l)for(const e in l){const t=O(e);if(ls(t)){const n=l[e],s=a[t]=f(n)||g(n)?{type:n}:c({},n);if(s){const e=us(Boolean,s.type),n=us(String,s.type);s[0]=e>-1,s[1]=n<0||e<n,(e>-1||p(s,"default"))&&u.push(t)}}}const h=[a,u];return _(e)&&r.set(e,h),h}function ls(e){return"$"!==e[0]}function as(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function cs(e,t){return as(e)===as(t)}function us(e,t){return f(t)?t.findIndex((t=>cs(t,e))):g(t)&&cs(t,e)?0:-1}const ds=e=>"_"===e[0]||"$stable"===e,ps=e=>f(e)?e.map(zs):[zs(e)],fs=(e,t,n)=>{if(t._n)return t;const s=function(e,t=Ht,n){if(!t)return e;if(e._n)return e;const s=(...n)=>{s._d&&Ms(-1);const o=Vt(t);let r;try{r=e(...n)}finally{Vt(o),s._d&&Ms(1)}return r};return s._n=!0,s._c=!0,s._d=!0,s}(((...e)=>ps(t(...e))),n);return s._c=!1,s},hs=(e,t,n)=>{const s=e._ctx;for(const n in e){if(ds(n))continue;const o=e[n];if(g(o))t[n]=fs(0,o,s);else if(null!=o){const e=ps(o);t[n]=()=>e}}},ms=(e,t)=>{const n=ps(t);e.slots.default=()=>n},gs=(e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=dt(t),L(t,"_",n)):hs(t,e.slots={})}else e.slots={},t&&ms(e,t);L(e.slots,Rs,1)},vs=(e,n,s)=>{const{vnode:o,slots:r}=e;let i=!0,l=t;if(32&o.shapeFlag){const e=n._;e?s&&1===e?i=!1:(c(r,n),s||1!==e||delete r._):(i=!n.$stable,hs(n,r)),l=n}else n&&(ms(e,n),l={default:1});if(i)for(const e in r)ds(e)||e in l||delete r[e]};function ys(e,n,s,o,r=!1){if(f(e))return void e.forEach(((e,t)=>ys(e,n&&(f(n)?n[t]:n),s,o,r)));if(hn(o)&&!r)return;const i=4&o.shapeFlag?uo(o.component)||o.component.proxy:o.el,l=r?null:i,{i:a,r:c}=e,d=n&&n.r,h=a.refs===t?a.refs={}:a.refs,m=a.setupState;if(null!=d&&d!==c&&(v(d)?(h[d]=null,p(m,d)&&(m[d]=null)):mt(d)&&(d.value=null)),g(c))_t(c,a,12,[l,h]);else{const t=v(c),n=mt(c);if(t||n){const o=()=>{if(e.f){const n=t?p(m,c)?m[c]:h[c]:c.value;r?f(n)&&u(n,i):f(n)?n.includes(i)||n.push(i):t?(h[c]=[i],p(m,c)&&(m[c]=h[c])):(c.value=[i],e.k&&(h[e.k]=c.value))}else t?(h[c]=l,p(m,c)&&(m[c]=l)):n&&(c.value=l,e.k&&(h[e.k]=l))};l?(o.id=-1,_s(o,s)):o()}}}const _s=function(e,t){var n;t&&t.pendingBranch?f(e)?t.effects.push(...e):t.effects.push(e):(f(n=e)?Et.push(...n):At&&At.includes(n,n.allowRecurse?kt+1:kt)||Et.push(n),Nt())};function bs(e,n){U().__VUE__=!0;const{insert:r,remove:i,patchProp:l,createElement:a,createText:c,createComment:u,setText:d,setElementText:f,parentNode:h,nextSibling:m,setScopeId:g=o,insertStaticContent:v}=e,y=(e,t,n,s=null,o=null,r=null,i=!1,l=null,a=!!t.dynamicChildren)=>{if(e===t)return;e&&!Ls(e,t)&&(s=X(e),G(e,o,r,!0),e=null),-2===t.patchFlag&&(a=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Cs:_(e,t,n,s);break;case Ts:w(e,t,n,s);break;case Es:null==e&&x(t,n,s,i);break;case Ss:N(e,t,n,s,o,r,i,l,a);break;default:1&d?S(e,t,n,s,o,r,i,l,a):6&d?$(e,t,n,s,o,r,i,l,a):(64&d||128&d)&&c.process(e,t,n,s,o,r,i,l,a,ee)}null!=u&&o&&ys(u,e&&e.ref,r,t||e,!t)},_=(e,t,n,s)=>{if(null==e)r(t.el=c(t.children),n,s);else{const n=t.el=e.el;t.children!==e.children&&d(n,t.children)}},w=(e,t,n,s)=>{null==e?r(t.el=u(t.children||""),n,s):t.el=e.el},x=(e,t,n,s)=>{[e.el,e.anchor]=v(e.children,t,n,s,e.el,e.anchor)},S=(e,t,n,s,o,r,i,l,a)=>{i=i||"svg"===t.type,null==e?C(t,n,s,o,r,i,l,a):k(e,t,o,r,i,l,a)},C=(e,t,n,s,o,i,c,u)=>{let d,p;const{type:h,props:m,shapeFlag:g,transition:v,dirs:y}=e;if(d=e.el=a(e.type,i,m&&m.is,m),8&g?f(d,e.children):16&g&&A(e.children,d,null,s,o,i&&"foreignObject"!==h,c,u),y&&nn(e,null,s,"created"),T(d,e,e.scopeId,c,s),m){for(const t in m)"value"===t||E(t)||l(d,t,null,m[t],i,e.children,s,o,J);"value"in m&&l(d,"value",null,m.value),(p=m.onVnodeBeforeMount)&&qs(p,s,e)}y&&nn(e,null,s,"beforeMount");const _=(!o||o&&!o.pendingBranch)&&v&&!v.persisted;_&&v.beforeEnter(d),r(d,t,n),((p=m&&m.onVnodeMounted)||_||y)&&_s((()=>{p&&qs(p,s,e),_&&v.enter(d),y&&nn(e,null,s,"mounted")}),o)},T=(e,t,n,s,o)=>{if(n&&g(e,n),s)for(let t=0;t<s.length;t++)g(e,s[t]);if(o&&t===o.subTree){const t=o.vnode;T(e,t,t.scopeId,t.slotScopeIds,o.parent)}},A=(e,t,n,s,o,r,i,l,a=0)=>{for(let c=a;c<e.length;c++){const a=e[c]=l?Gs(e[c]):zs(e[c]);y(null,a,t,n,s,o,r,i,l)}},k=(e,n,s,o,r,i,a)=>{const c=n.el=e.el;let{patchFlag:u,dynamicChildren:d,dirs:p}=n;u|=16&e.patchFlag;const h=e.props||t,m=n.props||t;let g;s&&ws(s,!1),(g=m.onVnodeBeforeUpdate)&&qs(g,s,n,e),p&&nn(n,e,s,"beforeUpdate"),s&&ws(s,!0);const v=r&&"foreignObject"!==n.type;if(d?I(e.dynamicChildren,d,c,s,o,v,i):a||H(e,n,c,null,s,o,v,i,!1),u>0){if(16&u)F(c,n,h,m,s,o,r);else if(2&u&&h.class!==m.class&&l(c,"class",null,m.class,r),4&u&&l(c,"style",h.style,m.style,r),8&u){const t=n.dynamicProps;for(let n=0;n<t.length;n++){const i=t[n],a=h[i],u=m[i];u===a&&"value"!==i||l(c,i,a,u,r,e.children,s,o,J)}}1&u&&e.children!==n.children&&f(c,n.children)}else a||null!=d||F(c,n,h,m,s,o,r);((g=m.onVnodeUpdated)||p)&&_s((()=>{g&&qs(g,s,n,e),p&&nn(n,e,s,"updated")}),o)},I=(e,t,n,s,o,r,i)=>{for(let l=0;l<t.length;l++){const a=e[l],c=t[l],u=a.el&&(a.type===Ss||!Ls(a,c)||70&a.shapeFlag)?h(a.el):n;y(a,c,u,null,s,o,r,i,!0)}},F=(e,n,s,o,r,i,a)=>{if(s!==o){if(s!==t)for(const t in s)E(t)||t in o||l(e,t,s[t],null,a,n.children,r,i,J);for(const t in o){if(E(t))continue;const c=o[t],u=s[t];c!==u&&"value"!==t&&l(e,t,u,c,a,n.children,r,i,J)}"value"in o&&l(e,"value",s.value,o.value)}},N=(e,t,n,s,o,i,l,a,u)=>{const d=t.el=e?e.el:c(""),p=t.anchor=e?e.anchor:c("");let{patchFlag:f,dynamicChildren:h,slotScopeIds:m}=t;m&&(a=a?a.concat(m):m),null==e?(r(d,n,s),r(p,n,s),A(t.children,n,p,o,i,l,a,u)):f>0&&64&f&&h&&e.dynamicChildren?(I(e.dynamicChildren,h,n,o,i,l,a),(null!=t.key||o&&t===o.subTree)&&xs(e,t,!0)):H(e,t,n,p,o,i,l,a,u)},$=(e,t,n,s,o,r,i,l,a)=>{t.slotScopeIds=l,null==e?512&t.shapeFlag?o.ctx.activate(t,n,s,i,a):R(t,n,s,o,r,i,a):j(e,t,a)},R=(e,n,s,o,r,i,l)=>{const a=e.component=function(e,n,s){const o=e.type,r=(n?n.appContext:e.appContext)||Zs,i={uid:Js++,vnode:e,type:o,parent:n,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new Q(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:is(o,r),emitsOptions:Ut(o,r),emit:null,emitted:null,propsDefaults:t,inheritAttrs:o.inheritAttrs,ctx:t,data:t,props:t,attrs:t,slots:t,refs:t,setupState:t,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=n?n.root:i,i.emit=Wt.bind(null,i),e.ce&&e.ce(i),i}(e,o,r);if(mn(e)&&(a.ctx.renderer=ee),function(e,t=!1){lo=t;const{props:n,children:s}=e.vnode,o=oo(e);(function(e,t,n,s=!1){const o={},r={};L(r,Rs,1),e.propsDefaults=Object.create(null),os(e,t,o,r);for(const t in e.propsOptions[0])t in o||(o[t]=void 0);n?e.props=s?o:it(o,!1,Fe,Qe,tt):e.type.props?e.props=o:e.props=r,e.attrs=r})(e,n,o,t),gs(e,s);const r=o?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=pt(new Proxy(e.ctx,Un));const{setup:s}=n;if(s){const n=e.setupContext=s.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(me(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null;no(e),fe();const o=_t(s,e,0,[e.props,n]);if(he(),so(),b(o)){if(o.then(so,so),t)return o.then((n=>{ao(e,n,t)})).catch((t=>{wt(t,e,0)}));e.asyncDep=o}else ao(e,o,t)}else co(e,t)}(e,t):void 0;lo=!1}(a),a.asyncDep){if(r&&r.registerDep(a,W),!e.el){const e=a.subTree=Bs(Ts);w(null,e,n,s)}}else W(a,e,n,s,r,i,l)},j=(e,t,n)=>{const s=t.component=e.component;if(function(e,t,n){const{props:s,children:o,component:r}=e,{props:i,children:l,patchFlag:a}=t,c=r.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&a>=0))return!(!o&&!l||l&&l.$stable)||s!==i&&(s?!i||qt(s,i,c):!!i);if(1024&a)return!0;if(16&a)return s?qt(s,i,c):!!i;if(8&a){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(i[n]!==s[n]&&!Bt(c,n))return!0}}return!1}(e,t,n)){if(s.asyncDep&&!s.asyncResolved)return void B(s,t,n);s.next=t,function(e){const t=Ct.indexOf(e);t>Tt&&Ct.splice(t,1)}(s.update),s.update()}else t.el=e.el,s.vnode=t},W=(e,t,n,s,o,r,i)=>{const l=e.effect=new ce((()=>{if(e.isMounted){let t,{next:n,bu:s,u:l,parent:a,vnode:c}=e,u=n;ws(e,!1),n?(n.el=c.el,B(e,n,i)):n=c,s&&P(s),(t=n.props&&n.props.onVnodeBeforeUpdate)&&qs(t,a,n,c),ws(e,!0);const d=zt(e),p=e.subTree;e.subTree=d,y(p,d,h(p.el),X(p),e,o,r),n.el=d.el,null===u&&function({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}(e,d.el),l&&_s(l,o),(t=n.props&&n.props.onVnodeUpdated)&&_s((()=>qs(t,a,n,c)),o)}else{let i;const{el:l,props:a}=t,{bm:c,m:u,parent:d}=e,p=hn(t);if(ws(e,!1),c&&P(c),!p&&(i=a&&a.onVnodeBeforeMount)&&qs(i,d,t),ws(e,!0),l&&ne){const n=()=>{e.subTree=zt(e),ne(l,e.subTree,e,o,null)};p?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const i=e.subTree=zt(e);y(null,i,n,s,e,o,r),t.el=i.el}if(u&&_s(u,o),!p&&(i=a&&a.onVnodeMounted)){const e=t;_s((()=>qs(i,d,e)),o)}(256&t.shapeFlag||d&&hn(d.vnode)&&256&d.vnode.shapeFlag)&&e.a&&_s(e.a,o),e.isMounted=!0,t=n=s=null}}),(()=>Ft(a)),e.scope),a=e.update=()=>l.run();a.id=e.uid,ws(e,!0),a()},B=(e,t,n)=>{t.component=e;const s=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,s){const{props:o,attrs:r,vnode:{patchFlag:i}}=e,l=dt(o),[a]=e.propsOptions;let c=!1;if(!(s||i>0)||16&i){let s;os(e,t,o,r)&&(c=!0);for(const r in l)t&&(p(t,r)||(s=M(r))!==r&&p(t,s))||(a?!n||void 0===n[r]&&void 0===n[s]||(o[r]=rs(a,l,r,void 0,e,!0)):delete o[r]);if(r!==l)for(const e in r)t&&p(t,e)||(delete r[e],c=!0)}else if(8&i){const n=e.vnode.dynamicProps;for(let s=0;s<n.length;s++){let i=n[s];if(Bt(e.emitsOptions,i))continue;const u=t[i];if(a)if(p(r,i))u!==r[i]&&(r[i]=u,c=!0);else{const t=O(i);o[t]=rs(a,l,t,u,e,!1)}else u!==r[i]&&(r[i]=u,c=!0)}}c&&ve(e,"set","$attrs")}(e,t.props,s,n),vs(e,t.children,n),fe(),$t(),he()},H=(e,t,n,s,o,r,i,l,a=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:p,shapeFlag:h}=t;if(p>0){if(128&p)return void V(c,d,n,s,o,r,i,l,a);if(256&p)return void D(c,d,n,s,o,r,i,l,a)}8&h?(16&u&&J(c,o,r),d!==c&&f(n,d)):16&u?16&h?V(c,d,n,s,o,r,i,l,a):J(c,o,r,!0):(8&u&&f(n,""),16&h&&A(d,n,s,o,r,i,l,a))},D=(e,t,n,o,r,i,l,a,c)=>{t=t||s;const u=(e=e||s).length,d=t.length,p=Math.min(u,d);let f;for(f=0;f<p;f++){const s=t[f]=c?Gs(t[f]):zs(t[f]);y(e[f],s,n,null,r,i,l,a,c)}u>d?J(e,r,i,!0,!1,p):A(t,n,o,r,i,l,a,c,p)},V=(e,t,n,o,r,i,l,a,c)=>{let u=0;const d=t.length;let p=e.length-1,f=d-1;for(;u<=p&&u<=f;){const s=e[u],o=t[u]=c?Gs(t[u]):zs(t[u]);if(!Ls(s,o))break;y(s,o,n,null,r,i,l,a,c),u++}for(;u<=p&&u<=f;){const s=e[p],o=t[f]=c?Gs(t[f]):zs(t[f]);if(!Ls(s,o))break;y(s,o,n,null,r,i,l,a,c),p--,f--}if(u>p){if(u<=f){const e=f+1,s=e<d?t[e].el:o;for(;u<=f;)y(null,t[u]=c?Gs(t[u]):zs(t[u]),n,s,r,i,l,a,c),u++}}else if(u>f)for(;u<=p;)G(e[u],r,i,!0),u++;else{const h=u,m=u,g=new Map;for(u=m;u<=f;u++){const e=t[u]=c?Gs(t[u]):zs(t[u]);null!=e.key&&g.set(e.key,u)}let v,_=0;const b=f-m+1;let w=!1,x=0;const S=new Array(b);for(u=0;u<b;u++)S[u]=0;for(u=h;u<=p;u++){const s=e[u];if(_>=b){G(s,r,i,!0);continue}let o;if(null!=s.key)o=g.get(s.key);else for(v=m;v<=f;v++)if(0===S[v-m]&&Ls(s,t[v])){o=v;break}void 0===o?G(s,r,i,!0):(S[o-m]=u+1,o>=x?x=o:w=!0,y(s,t[o],n,null,r,i,l,a,c),_++)}const C=w?function(e){const t=e.slice(),n=[0];let s,o,r,i,l;const a=e.length;for(s=0;s<a;s++){const a=e[s];if(0!==a){if(o=n[n.length-1],e[o]<a){t[s]=o,n.push(s);continue}for(r=0,i=n.length-1;r<i;)l=r+i>>1,e[n[l]]<a?r=l+1:i=l;a<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}(S):s;for(v=C.length-1,u=b-1;u>=0;u--){const e=m+u,s=t[e],p=e+1<d?t[e+1].el:o;0===S[u]?y(null,s,n,p,r,i,l,a,c):w&&(v<0||u!==C[v]?z(s,n,p,2):v--)}}},z=(e,t,n,s,o=null)=>{const{el:i,type:l,transition:a,children:c,shapeFlag:u}=e;if(6&u)z(e.component.subTree,t,n,s);else if(128&u)e.suspense.move(t,n,s);else if(64&u)l.move(e,t,n,ee);else if(l!==Ss)if(l!==Es)if(2!==s&&1&u&&a)if(0===s)a.beforeEnter(i),r(i,t,n),_s((()=>a.enter(i)),o);else{const{leave:e,delayLeave:s,afterLeave:o}=a,l=()=>r(i,t,n),c=()=>{e(i,(()=>{l(),o&&o()}))};s?s(i,l,c):c()}else r(i,t,n);else(({el:e,anchor:t},n,s)=>{let o;for(;e&&e!==t;)o=m(e),r(e,n,s),e=o;r(t,n,s)})(e,t,n);else{r(i,t,n);for(let e=0;e<c.length;e++)z(c[e],t,n,s);r(e.anchor,t,n)}},G=(e,t,n,s=!1,o=!1)=>{const{type:r,props:i,ref:l,children:a,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:p}=e;if(null!=l&&ys(l,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const f=1&u&&p,h=!hn(e);let m;if(h&&(m=i&&i.onVnodeBeforeUnmount)&&qs(m,t,e),6&u)Z(e.component,n,s);else{if(128&u)return void e.suspense.unmount(n,s);f&&nn(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,o,ee,s):c&&(r!==Ss||d>0&&64&d)?J(c,t,n,!1,!0):(r===Ss&&384&d||!o&&16&u)&&J(a,t,n),s&&K(e)}(h&&(m=i&&i.onVnodeUnmounted)||f)&&_s((()=>{m&&qs(m,t,e),f&&nn(e,null,t,"unmounted")}),n)},K=e=>{const{type:t,el:n,anchor:s,transition:o}=e;if(t===Ss)return void q(n,s);if(t===Es)return void(({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=m(e),i(e),e=n;i(t)})(e);const r=()=>{i(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:s}=o,i=()=>t(n,r);s?s(e.el,r,i):i()}else r()},q=(e,t)=>{let n;for(;e!==t;)n=m(e),i(e),e=n;i(t)},Z=(e,t,n)=>{const{bum:s,scope:o,update:r,subTree:i,um:l}=e;s&&P(s),o.stop(),r&&(r.active=!1,G(i,e,t,n)),l&&_s(l,t),_s((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},J=(e,t,n,s=!1,o=!1,r=0)=>{for(let i=r;i<e.length;i++)G(e[i],t,n,s,o)},X=e=>6&e.shapeFlag?X(e.component.subTree):128&e.shapeFlag?e.suspense.next():m(e.anchor||e.el),Y=(e,t,n)=>{null==e?t._vnode&&G(t._vnode,null,null,!0):y(t._vnode||null,e,t,null,null,null,n),$t(),Pt(),t._vnode=e},ee={p:y,um:G,m:z,r:K,mt:R,mc:A,pc:H,pbc:I,n:X,o:e};let te,ne;return n&&([te,ne]=n(ee)),{render:Y,hydrate:te,createApp:ts(Y,te)}}function ws({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function xs(e,t,n=!1){const s=e.children,o=t.children;if(f(s)&&f(o))for(let e=0;e<s.length;e++){const t=s[e];let r=o[e];1&r.shapeFlag&&!r.dynamicChildren&&((r.patchFlag<=0||32===r.patchFlag)&&(r=o[e]=Gs(o[e]),r.el=t.el),n||xs(t,r)),r.type===Cs&&(r.el=t.el)}}const Ss=Symbol.for("v-fgt"),Cs=Symbol.for("v-txt"),Ts=Symbol.for("v-cmt"),Es=Symbol.for("v-stc"),As=[];let ks=null;function Os(e=!1){As.push(ks=e?null:[])}let Is=1;function Ms(e){Is+=e}function Fs(e){return e.dynamicChildren=Is>0?ks||s:null,As.pop(),ks=As[As.length-1]||null,Is>0&&ks&&ks.push(e),e}function Ns(e,t,n,s,o,r){return Fs(Us(e,t,n,s,o,r,!0))}function $s(e,t,n,s,o){return Fs(Bs(e,t,n,s,o,!0))}function Ps(e){return!!e&&!0===e.__v_isVNode}function Ls(e,t){return e.type===t.type&&e.key===t.key}const Rs="__vInternal",js=({key:e})=>null!=e?e:null,Ws=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||mt(e)||g(e)?{i:Ht,r:e,k:t,f:!!n}:e:null);function Us(e,t=null,n=null,s=0,o=null,r=(e===Ss?0:1),i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&js(t),ref:t&&Ws(t),scopeId:Dt,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Ht};return l?(Ks(a,n),128&r&&e.normalize(a)):n&&(a.shapeFlag|=v(n)?8:16),Is>0&&!i&&ks&&(a.patchFlag>0||6&r)&&32!==a.patchFlag&&ks.push(a),a}const Bs=function(e,t=null,n=null,s=0,o=null,r=!1){if(e&&e!==$n||(e=Ts),Ps(e)){const s=Hs(e,t,!0);return n&&Ks(s,n),Is>0&&!r&&ks&&(6&s.shapeFlag?ks[ks.indexOf(e)]=s:ks.push(s)),s.patchFlag|=-2,s}if(i=e,g(i)&&"__vccOpts"in i&&(e=e.__vccOpts),t){t=function(e){return e?ut(e)||Rs in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=G(e)),_(n)&&(ut(n)&&!f(n)&&(n=c({},n)),t.style=B(n))}var i;return Us(e,t,n,s,o,v(e)?1:(e=>e.__isSuspense)(e)?128:(e=>e.__isTeleport)(e)?64:_(e)?4:g(e)?2:0,r,!0)};function Hs(e,t,n=!1){const{props:s,ref:o,patchFlag:r,children:i}=e,a=t?function(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const e in s)if("class"===e)t.class!==s.class&&(t.class=G([t.class,s.class]));else if("style"===e)t.style=B([t.style,s.style]);else if(l(e)){const n=t[e],o=s[e];!o||n===o||f(n)&&n.includes(o)||(t[e]=n?[].concat(n,o):o)}else""!==e&&(t[e]=s[e])}return t}(s||{},t):s;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&js(a),ref:t&&t.ref?n&&o?f(o)?o.concat(Ws(t)):[o,Ws(t)]:Ws(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:i,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ss?-1===r?16:16|r:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Hs(e.ssContent),ssFallback:e.ssFallback&&Hs(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Ds(e=" ",t=0){return Bs(Cs,null,e,t)}function Vs(e="",t=!1){return t?(Os(),$s(Ts,null,e)):Bs(Ts,null,e)}function zs(e){return null==e||"boolean"==typeof e?Bs(Ts):f(e)?Bs(Ss,null,e.slice()):"object"==typeof e?Gs(e):Bs(Cs,null,String(e))}function Gs(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Hs(e)}function Ks(e,t){let n=0;const{shapeFlag:s}=e;if(null==t)t=null;else if(f(t))n=16;else if("object"==typeof t){if(65&s){const n=t.default;return void(n&&(n._c&&(n._d=!1),Ks(e,n()),n._c&&(n._d=!0)))}{n=32;const s=t._;s||Rs in t?3===s&&Ht&&(1===Ht.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Ht}}else g(t)?(t={default:t,_ctx:Ht},n=32):(t=String(t),64&s?(n=16,t=[Ds(t)]):n=8);e.children=t,e.shapeFlag|=n}function qs(e,t,n,s=null){bt(e,t,7,[n,s])}const Zs=Yn();let Js=0;let Xs=null;const Qs=()=>Xs||Ht;let Ys,eo,to="__VUE_INSTANCE_SETTERS__";(eo=U()[to])||(eo=U()[to]=[]),eo.push((e=>Xs=e)),Ys=e=>{eo.length>1?eo.forEach((t=>t(e))):eo[0](e)};const no=e=>{Ys(e),e.scope.on()},so=()=>{Xs&&Xs.scope.off(),Ys(null)};function oo(e){return 4&e.vnode.shapeFlag}let ro,io,lo=!1;function ao(e,t,n){g(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=vt(t)),co(e,n)}function co(e,t,n){const s=e.type;if(!e.render){if(!t&&ro&&!s.render){const t=s.template||zn(e).template;if(t){const{isCustomElement:n,compilerOptions:o}=e.appContext.config,{delimiters:r,compilerOptions:i}=s,l=c(c({isCustomElement:n,delimiters:r},o),i);s.render=ro(t,l)}}e.render=s.render||o,io&&io(e)}no(e),fe(),function(e){const t=zn(e),n=e.proxy,s=e.ctx;Hn=!1,t.beforeCreate&&Dn(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:l,watch:a,provide:c,inject:u,created:d,beforeMount:p,mounted:h,beforeUpdate:m,updated:v,activated:y,deactivated:b,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:C,render:T,renderTracked:E,renderTriggered:A,errorCaptured:k,serverPrefetch:O,expose:I,inheritAttrs:M,components:F,directives:N,filters:$}=t;if(u&&function(e,t,n=o){f(e)&&(e=Zn(e));for(const n in e){const s=e[n];let o;o=_(s)?"default"in s?ss(s.from||n,s.default,!0):ss(s.from||n):ss(s),mt(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:e=>o.value=e}):t[n]=o}}(u,s,null),l)for(const e in l){const t=l[e];g(t)&&(s[e]=t.bind(n))}if(r){const t=r.call(n,n);_(t)&&(e.data=ot(t))}if(Hn=!0,i)for(const e in i){const t=i[e],r=g(t)?t.bind(n,n):g(t.get)?t.get.bind(n,n):o,l=!g(t)&&g(t.set)?t.set.bind(n):o,a=po({get:r,set:l});Object.defineProperty(s,e,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(a)for(const e in a)Vn(a[e],s,n,e);if(c){const e=g(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{!function(e,t){if(Xs){let n=Xs.provides;const s=Xs.parent&&Xs.parent.provides;s===n&&(n=Xs.provides=Object.create(s)),n[e]=t}}(t,e[t])}))}function P(e,t){f(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&Dn(d,e,"c"),P(xn,p),P(Sn,h),P(Cn,m),P(Tn,v),P(gn,y),P(vn,b),P(Mn,k),P(In,E),P(On,A),P(En,x),P(An,C),P(kn,O),f(I))if(I.length){const t=e.exposed||(e.exposed={});I.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});T&&e.render===o&&(e.render=T),null!=M&&(e.inheritAttrs=M),F&&(e.components=F),N&&(e.directives=N)}(e),he(),so()}function uo(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(vt(pt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in jn?jn[n](e):void 0,has:(e,t)=>t in e||t in jn}))}const po=(e,t)=>function(e,t,n=!1){let s,r;const i=g(e);return i?(s=e,r=o):(s=e.get,r=e.set),new yt(s,r,i||!r,n)}(e,0,lo),fo=Symbol.for("v-scx"),ho=()=>ss(fo),mo="3.3.4",go="undefined"!=typeof document?document:null,vo=go&&go.createElement("template"),yo={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const o=t?go.createElementNS("http://www.w3.org/2000/svg",e):go.createElement(e,n?{is:n}:void 0);return"select"===e&&s&&null!=s.multiple&&o.setAttribute("multiple",s.multiple),o},createText:e=>go.createTextNode(e),createComment:e=>go.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>go.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,o,r){const i=n?n.previousSibling:t.lastChild;if(o&&(o===r||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),o!==r&&(o=o.nextSibling););else{vo.innerHTML=s?`<svg>${e}</svg>`:e;const o=vo.content;if(s){const e=o.firstChild;for(;e.firstChild;)o.appendChild(e.firstChild);o.removeChild(e)}t.insertBefore(o,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},_o=/\s*!important$/;function bo(e,t,n){if(f(n))n.forEach((n=>bo(e,t,n)));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=function(e,t){const n=xo[t];if(n)return n;let s=O(t);if("filter"!==s&&s in e)return xo[t]=s;s=F(s);for(let n=0;n<wo.length;n++){const o=wo[n]+s;if(o in e)return xo[t]=o}return t}(e,t);_o.test(n)?e.setProperty(M(s),n.replace(_o,""),"important"):e[s]=n}}const wo=["Webkit","Moz","ms"],xo={},So="http://www.w3.org/1999/xlink";function Co(e,t,n,s){e.addEventListener(t,n,s)}const To=/(?:Once|Passive|Capture)$/;let Eo=0;const Ao=Promise.resolve(),ko=()=>Eo||(Ao.then((()=>Eo=0)),Eo=Date.now()),Oo=/^on[a-z]/;"undefined"!=typeof HTMLElement&&HTMLElement;const Io="transition",Mo="animation",Fo=(e,{slots:t})=>function(e,t,n){const s=arguments.length;return 2===s?_(t)&&!f(t)?Ps(t)?Bs(e,null,[t]):Bs(e,t):Bs(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):3===s&&Ps(n)&&(n=[n]),Bs(e,t,n))}(rn,function(e){const t={};for(const n in e)n in No||(t[n]=e[n]);if(!1===e.css)return t;const{name:n="v",type:s,duration:o,enterFromClass:r=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=r,appearActiveClass:u=i,appearToClass:d=l,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,m=function(e){if(null==e)return null;if(_(e))return[Lo(e.enter),Lo(e.leave)];{const t=Lo(e);return[t,t]}}(o),g=m&&m[0],v=m&&m[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:w,onLeave:x,onLeaveCancelled:S,onBeforeAppear:C=y,onAppear:T=b,onAppearCancelled:E=w}=t,A=(e,t,n)=>{jo(e,t?d:l),jo(e,t?u:i),n&&n()},k=(e,t)=>{e._isLeaving=!1,jo(e,p),jo(e,h),jo(e,f),t&&t()},O=e=>(t,n)=>{const o=e?T:b,i=()=>A(t,e,n);$o(o,[t,i]),Wo((()=>{jo(t,e?a:r),Ro(t,e?d:l),Po(o)||Bo(t,s,g,i)}))};return c(t,{onBeforeEnter(e){$o(y,[e]),Ro(e,r),Ro(e,i)},onBeforeAppear(e){$o(C,[e]),Ro(e,a),Ro(e,u)},onEnter:O(!1),onAppear:O(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>k(e,t);Ro(e,p),document.body.offsetHeight,Ro(e,f),Wo((()=>{e._isLeaving&&(jo(e,p),Ro(e,h),Po(x)||Bo(e,s,v,n))})),$o(x,[e,n])},onEnterCancelled(e){A(e,!1),$o(w,[e])},onAppearCancelled(e){A(e,!0),$o(E,[e])},onLeaveCancelled(e){k(e),$o(S,[e])}})}(e),t);Fo.displayName="Transition";const No={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},$o=(Fo.props=c({},on,No),(e,t=[])=>{f(e)?e.forEach((e=>e(...t))):e&&e(...t)}),Po=e=>!!e&&(f(e)?e.some((e=>e.length>1)):e.length>1);function Lo(e){return j(e)}function Ro(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function jo(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function Wo(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Uo=0;function Bo(e,t,n,s){const o=e._endId=++Uo,r=()=>{o===e._endId&&s()};if(n)return setTimeout(r,n);const{type:i,timeout:l,propCount:a}=function(e,t){const n=window.getComputedStyle(e),s=e=>(n[e]||"").split(", "),o=s(`${Io}Delay`),r=s(`${Io}Duration`),i=Ho(o,r),l=s(`${Mo}Delay`),a=s(`${Mo}Duration`),c=Ho(l,a);let u=null,d=0,p=0;return t===Io?i>0&&(u=Io,d=i,p=r.length):t===Mo?c>0&&(u=Mo,d=c,p=a.length):(d=Math.max(i,c),u=d>0?i>c?Io:Mo:null,p=u?u===Io?r.length:a.length:0),{type:u,timeout:d,propCount:p,hasTransform:u===Io&&/\b(transform|all)(,|$)/.test(s(`${Io}Property`).toString())}}(e,t);if(!i)return s();const c=i+"end";let u=0;const d=()=>{e.removeEventListener(c,p),r()},p=t=>{t.target===e&&++u>=a&&d()};setTimeout((()=>{u<a&&d()}),l+1),e.addEventListener(c,p)}function Ho(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Do(t)+Do(e[n]))))}function Do(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}const Vo=e=>{const t=e.props["onUpdate:modelValue"]||!1;return f(t)?e=>P(t,e):t};function zo(e){e.target.composing=!0}function Go(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ko={created(e,{modifiers:{lazy:t,trim:n,number:s}},o){e._assign=Vo(o);const r=s||o.props&&"number"===o.props.type;Co(e,t?"change":"input",(t=>{if(t.target.composing)return;let s=e.value;n&&(s=s.trim()),r&&(s=R(s)),e._assign(s)})),n&&Co(e,"change",(()=>{e.value=e.value.trim()})),t||(Co(e,"compositionstart",zo),Co(e,"compositionend",Go),Co(e,"change",Go))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:s,number:o}},r){if(e._assign=Vo(r),e.composing)return;if(document.activeElement===e&&"range"!==e.type){if(n)return;if(s&&e.value.trim()===t)return;if((o||"number"===e.type)&&R(e.value)===t)return}const i=null==t?"":t;e.value!==i&&(e.value=i)}},qo=["ctrl","shift","alt","meta"],Zo={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>qo.some((n=>e[`${n}Key`]&&!t.includes(n)))},Jo={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Xo=(e,t)=>n=>{if(!("key"in n))return;const s=M(n.key);return t.some((e=>e===s||Jo[e]===s))?e(n):void 0},Qo={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Yo(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),Yo(e,!0),s.enter(e)):s.leave(e,(()=>{Yo(e,!1)})):Yo(e,t))},beforeUnmount(e,{value:t}){Yo(e,t)}};function Yo(e,t){e.style.display=t?e._vod:"none"}const er=c({patchProp:(e,t,n,s,o=!1,r,i,c,u)=>{"class"===t?function(e,t,n){const s=e._vtc;s&&(t=(t?[t,...s]:[...s]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,s,o):"style"===t?function(e,t,n){const s=e.style,o=v(n);if(n&&!o){if(t&&!v(t))for(const e in t)null==n[e]&&bo(s,e,"");for(const e in n)bo(s,e,n[e])}else{const r=s.display;o?t!==n&&(s.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(s.display=r)}}(e,n,s):l(t)?a(t)||function(e,t,n,s,o=null){const r=e._vei||(e._vei={}),i=r[t];if(s&&i)i.value=s;else{const[n,l]=function(e){let t;if(To.test(e)){let n;for(t={};n=e.match(To);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):M(e.slice(2)),t]}(t);if(s){const i=r[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();bt(function(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=ko(),n}(s,o);Co(e,n,i,l)}else i&&(function(e,t,n,s){e.removeEventListener(t,n,s)}(e,n,i,l),r[t]=void 0)}}(e,t,0,s,i):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,s){return s?"innerHTML"===t||"textContent"===t||!!(t in e&&Oo.test(t)&&g(n)):"spellcheck"!==t&&"draggable"!==t&&"translate"!==t&&("form"!==t&&(("list"!==t||"INPUT"!==e.tagName)&&(("type"!==t||"TEXTAREA"!==e.tagName)&&((!Oo.test(t)||!v(n))&&t in e))))}(e,t,s,o))?function(e,t,n,s,o,r,i){if("innerHTML"===t||"textContent"===t)return s&&i(s,o,r),void(e[t]=null==n?"":n);const l=e.tagName;if("value"===t&&"PROGRESS"!==l&&!l.includes("-")){e._value=n;const s=null==n?"":n;return("OPTION"===l?e.getAttribute("value"):e.value)!==s&&(e.value=s),void(null==n&&e.removeAttribute(t))}let a=!1;if(""===n||null==n){const s=typeof e[t];"boolean"===s?n=q(n):null==n&&"string"===s?(n="",a=!0):"number"===s&&(n=0,a=!0)}try{e[t]=n}catch(e){}a&&e.removeAttribute(t)}(e,t,s,r,i,c,u):("true-value"===t?e._trueValue=s:"false-value"===t&&(e._falseValue=s),function(e,t,n,s,o){if(s&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(So,t.slice(6,t.length)):e.setAttributeNS(So,t,n);else{const s=K(t);null==n||s&&!q(n)?e.removeAttribute(t):e.setAttribute(t,s?"":n)}}(e,t,s,o))}},yo);let tr;const nr={id:"app"},sr={class:"chat-messages",ref:"messages"},or={class:"chat-input"},rr={class:"input"};function ir(e,t){var n=new XMLHttpRequest;n.open("POST",e,!0),n.setRequestHeader("Content-Type","application/json; charset=UTF-8"),n.send(t)}function lr(e,t={}){const n=Object.assign({type:e},t);window.dispatchEvent(new CustomEvent("message",{detail:n}))}window.emulate=lr,window.demo=()=>{lr("ON_MESSAGE",{message:{args:["me","hello!"]}}),lr("ON_SCREEN_STATE_CHANGE",{shouldHide:!1})};const ar={defaultTemplateId:"default",defaultAltTemplateId:"defaultAlt",templates:{default:"<b>{0}</b>: {1}",defaultAlt:"{0}",print:"<pre>{0}</pre>","example:important":"<h1>^2{0}</h1>"},fadeTimeout:7e3,suggestionLimit:5,style:{background:"rgba(52, 73, 94, 0.7)",width:"38vw",height:"22%"}},cr={class:"suggestions-wrap"},ur={class:"suggestions"},dr={class:"help"},pr=fn({props:{message:{type:String,required:!0},suggestions:{type:Array,required:!0}},data:()=>({}),computed:{currentSuggestions(){if(""===this.message)return[];const e=this.suggestions.filter((e=>{if(!e.name.startsWith(this.message)){const t=e.name.split(" "),n=this.message.split(" ");for(let s=0;s<n.length;s+=1){if(s>=t.length)return s<t.length+e.params.length;if(t[s]!==n[s])return!1}}return!0})).slice(0,ar.suggestionLimit);return e.forEach((e=>{e.disabled=!e.name.startsWith(this.message),e.params.forEach(((t,n)=>{const s=n===e.params.length-1?".":"\\S",o=new RegExp(`${e.name} (?:\\w+ ){${n}}(?:${s}*)$`,"g");t.disabled=null==this.message.match(o)}))})),e}},methods:{}});var fr=n(744);const hr=(0,fr.Z)(pr,[["render",function(e,t,n,s,o,r){return tn((Os(),Ns("div",cr,[Us("ul",ur,[(Os(!0),Ns(Ss,null,Ln(e.currentSuggestions,(e=>(Os(),Ns("li",{class:"suggestion",key:e.name},[Us("p",null,[Us("span",{class:G({disabled:e.disabled})},Z(e.name),3),(Os(!0),Ns(Ss,null,Ln(e.params,(e=>(Os(),Ns("span",{class:G(["param",{disabled:e.disabled}]),key:e.name}," ["+Z(e.name)+"] ",3)))),128))]),Us("small",dr,[e.disabled?Vs("v-if",!0):(Os(),Ns(Ss,{key:0},[Ds(Z(e.help),1)],64)),(Os(!0),Ns(Ss,null,Ln(e.params,(e=>(Os(),Ns(Ss,null,[e.disabled?Vs("v-if",!0):(Os(),Ns(Ss,{key:0},[Ds(Z(e.help),1)],64))],64)))),256))])])))),128))])],512)),[[Qo,e.currentSuggestions.length>0]])}]]),mr=hr,gr=["innerHTML"],vr=fn({data:()=>({}),computed:{textEscaped(){let e=this.template?this.template:this.templates[this.templateId];this.template||this.templateId!=ar.defaultTemplateId||1!=this.args.length||(e=this.templates[ar.defaultAltTemplateId]),e=e.replace("@default",this.templates[this.templateId]),e=e.replace(/{(\d+)}/g,((e,t)=>{const n=null!=this.args[t]?this.escape(this.args[t]):e;return 0==t&&this.color?this.colorizeOld(n):n}));const t=this.params;return t&&(e=e.replace(/\{\{([a-zA-Z0-9_\-]+?)\}\}/g,((e,n)=>null!=t[n]?this.escape(t[n]):e))),this.colorize(e)}},methods:{colorizeOld(e){return`<span style="color: rgb(${this.color[0]}, ${this.color[1]}, ${this.color[2]})">${e}</span>`},colorize(e){let t="<span>"+function(e){return e.replace(/\^([0-9])/g,((e,t)=>`</span><span class="color-${t}">`)).replace(/\^#([0-9A-F]{3,6})/gi,((e,t)=>`</span><span class="color" style="color: #${t}">`)).replace(/~([a-z])~/g,((e,t)=>`</span><span class="gameColor-${t}">`))}(e)+"</span>";const n={"*":"font-weight: bold;",_:"text-decoration: underline;","~":"text-decoration: line-through;","=":"text-decoration: underline line-through;",r:"text-decoration: none;font-weight: normal;"},s=/\^(\_|\*|\=|\~|\/|r)(.*?)(?=$|\^r|<\/em>)/;for(;t.match(s);)t=t.replace(s,((e,t,s)=>`<em style="${n[t]}">${s}</em>`));return t.replace(/<span[^>]*><\/span[^>]*>/g,"")},escape:e=>String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")},props:{templates:{type:Object,required:!0},args:{type:Array,required:!0},params:{type:Object},template:{type:String,default:null},templateId:{type:String,default:ar.defaultTemplateId},multiline:{type:Boolean,default:!1},color:{type:Array,default:null}}}),yr=(0,fr.Z)(vr,[["render",function(e,t,n,s,o,r){return Os(),Ns("div",{class:G(["msg",{multiline:e.multiline}])},[Us("span",{innerHTML:e.textEscaped},null,8,gr)],2)}]]);var _r;!function(e){e[e.ShowWhenActive=0]="ShowWhenActive",e[e.AlwaysShow=1]="AlwaysShow",e[e.AlwaysHide=2]="AlwaysHide"}(_r||(_r={}));const br={name:"all",displayName:"All",color:"#fff"},wr={name:"_global",displayName:"All",color:"#fff",isGlobal:!0,hidden:!0},xr=fn({template:"#app_template",name:"app",components:{Suggestions:mr,Message:yr},data:()=>({style:ar.style,showInput:!1,showWindow:!1,showHideState:!1,hideState:_r.ShowWhenActive,backingSuggestions:[],removedSuggestions:[],templates:Object.assign({},ar.templates),message:"",messages:[],oldMessages:[],oldMessagesIndex:-1,tplBackups:[],msgTplBackups:[],focusTimer:0,showWindowTimer:0,showHideStateTimer:0,listener:e=>{},modes:[br,wr],modeIdx:0}),destroyed(){clearInterval(this.focusTimer),window.removeEventListener("message",this.listener)},mounted(){ir("http://chat/loaded",JSON.stringify({})),this.listener=e=>{const t=e.data||e.detail;if(!t||!t.type)return;const n=t.type;this[n]&&this[n](t)},window.addEventListener("message",this.listener)},watch:{messages(){this.hideState!==_r.AlwaysHide&&(this.showWindowTimer&&clearTimeout(this.showWindowTimer),this.showWindow=!0,this.resetShowWindowTimer());const e=this.$refs.messages;this.$nextTick((()=>{e.scrollTop=e.scrollHeight}))}},computed:{filteredMessages(){return this.messages.filter((e=>{var t,n;return!(null===(t=e.modeData)||void 0===t?void 0:t.isChannel)&&!this.modes[this.modeIdx].isChannel||e.mode===this.modes[this.modeIdx].name||(null===(n=e.modeData)||void 0===n?void 0:n.isGlobal)}))},suggestions(){return this.backingSuggestions.filter((e=>this.removedSuggestions.indexOf(e.name)<=-1))},hideAnimated(){return this.hideState!==_r.AlwaysHide},modeIdxGet(){return this.modeIdx>=this.modes.length?this.modes.length-1:this.modeIdx},modePrefix(){return 2===this.modes.length?"➤":this.modes[this.modeIdxGet].displayName},modeColor(){return this.modes[this.modeIdxGet].color},hideStateString(){switch(this.hideState){case _r.AlwaysShow:return"Visible";case _r.AlwaysHide:return"Hidden";case _r.ShowWhenActive:return"When active"}}},methods:{ON_SCREEN_STATE_CHANGE({hideState:e,fromUserInteraction:t}){this.hideState=e,this.hideState===_r.AlwaysHide?this.showInput||(this.showWindow=!1):this.hideState===_r.AlwaysShow?(this.showWindow=!0,this.showWindowTimer&&clearTimeout(this.showWindowTimer)):this.resetShowWindowTimer(),t&&(this.showHideState=!0,this.showHideStateTimer&&clearTimeout(this.showHideStateTimer),this.showHideStateTimer=window.setTimeout((()=>{this.showHideState=!1}),1500))},ON_OPEN(){this.showInput=!0,this.showWindow=!0,this.showWindowTimer&&clearTimeout(this.showWindowTimer),this.focusTimer=window.setInterval((()=>{this.$refs.input?this.$refs.input.focus():clearInterval(this.focusTimer)}),100)},ON_MESSAGE({message:e}){e.id=`${(new Date).getTime()}${Math.random()}`,e.modeData=this.modes.find((t=>t.name===e.mode)),this.messages.push(e)},ON_CLEAR(){this.messages=[],this.oldMessages=[],this.oldMessagesIndex=-1},ON_SUGGESTION_ADD({suggestion:e}){const t=Array.isArray(e)?e:[e];for(const e of t){this.removedSuggestions=this.removedSuggestions.filter((t=>t!==e.name));const t=this.backingSuggestions.find((t=>t.name===e.name));t?(e.help||e.params)&&(t.help=e.help||"",t.params=e.params||[]):(e.params||(e.params=[]),this.backingSuggestions.push(e))}},ON_SUGGESTION_REMOVE({name:e}){this.removedSuggestions.indexOf(e)<=-1&&this.removedSuggestions.push(e)},ON_MODE_ADD({mode:e}){this.modes=[...this.modes.filter((t=>t.name!==e.name)),e]},ON_MODE_REMOVE({name:e}){this.modes=this.modes.filter((t=>t.name!==e)),0===this.modes.length&&(this.modes=[br])},ON_TEMPLATE_ADD({template:e}){this.templates[e.id]?this.warn(`Tried to add duplicate template '${e.id}'`):this.templates[e.id]=e.html},ON_UPDATE_THEMES({themes:e}){this.removeThemes(),this.setThemes(e)},removeThemes(){var e;for(let t=0;t<document.styleSheets.length;t++){const n=document.styleSheets[t].ownerNode;n.getAttribute("data-theme")&&(null===(e=n.parentNode)||void 0===e||e.removeChild(n))}this.tplBackups.reverse();for(const[e,t]of this.tplBackups)e.innerText=t;this.tplBackups=[],this.msgTplBackups.reverse();for(const[e,t]of this.msgTplBackups)this.templates[e]=t;this.msgTplBackups=[]},setThemes(e){for(const[t,n]of Object.entries(e)){if(n.style){const e=document.createElement("style");e.type="text/css",e.setAttribute("data-theme",t),e.appendChild(document.createTextNode(n.style)),document.head.appendChild(e)}if(n.styleSheet){const e=document.createElement("link");e.rel="stylesheet",e.type="text/css",e.href=n.baseUrl+n.styleSheet,e.setAttribute("data-theme",t),document.head.appendChild(e)}if(n.templates)for(const[e,t]of Object.entries(n.templates)){const n=document.getElementById(e);n&&(this.tplBackups.push([n,n.innerText]),n.innerText=t)}if(n.script){const e=document.createElement("script");e.type="text/javascript",e.src=n.baseUrl+n.script,document.head.appendChild(e)}if(n.msgTemplates)for(const[e,t]of Object.entries(n.msgTemplates))this.msgTplBackups.push([e,this.templates[e]]),this.templates[e]=t}},warn(e){this.messages.push({args:[e],template:"^3<b>CHAT-WARN</b>: ^0{0}"})},clearShowWindowTimer(){clearTimeout(this.showWindowTimer)},resetShowWindowTimer(){this.clearShowWindowTimer(),this.showWindowTimer=window.setTimeout((()=>{this.hideState===_r.AlwaysShow||this.showInput||(this.showWindow=!1)}),ar.fadeTimeout)},keyUp(){this.resize()},keyDown(e){if(38===e.which||40===e.which)e.preventDefault(),this.moveOldMessageIndex(38===e.which);else if(33==e.which)(t=document.getElementsByClassName("chat-messages")[0]).scrollTop=t.scrollTop-100;else if(34==e.which){var t;(t=document.getElementsByClassName("chat-messages")[0]).scrollTop=t.scrollTop+100}else if(9===e.which){if(e.shiftKey||e.altKey)do{--this.modeIdx,this.modeIdx<0&&(this.modeIdx=this.modes.length-1)}while(this.modes[this.modeIdx].hidden);else do{this.modeIdx=(this.modeIdx+1)%this.modes.length}while(this.modes[this.modeIdx].hidden);const t=document.getElementsByClassName("chat-messages")[0];setTimeout((()=>t.scrollTop=t.scrollHeight),0)}this.resize()},moveOldMessageIndex(e){e&&this.oldMessages.length>this.oldMessagesIndex+1?(this.oldMessagesIndex+=1,this.message=this.oldMessages[this.oldMessagesIndex]):!e&&this.oldMessagesIndex-1>=0?(this.oldMessagesIndex-=1,this.message=this.oldMessages[this.oldMessagesIndex]):e||this.oldMessagesIndex-1!=-1||(this.oldMessagesIndex=-1,this.message="")},resize(){const e=this.$refs.input,t=getComputedStyle(e),n=parseFloat(t.paddingBottom)+parseFloat(t.paddingTop);e.style.height="5px",e.style.height=e.scrollHeight-n+"px"},send(){""!==this.message?(ir("http://chat/chatResult",JSON.stringify({message:this.message,mode:this.modes[this.modeIdxGet].name})),this.oldMessages.unshift(this.message),this.oldMessagesIndex=-1,this.hideInput()):this.hideInput(!0)},hideInput(e=!1){setTimeout((()=>{this.$refs.input.style.height="auto"}),50),e&&ir("http://chat/chatResult",JSON.stringify({canceled:e})),this.message="",this.showInput=!1,clearInterval(this.focusTimer),this.hideState!==_r.AlwaysHide?this.resetShowWindowTimer():this.showWindow=!1}}});((...e)=>{const t=(tr||(tr=bs(er))).createApp(...e),{mount:n}=t;return t.mount=e=>{const s=function(e){return v(e)?document.querySelector(e):e}(e);if(!s)return;const o=t._component;g(o)||o.render||o.template||(o.template=s.innerHTML),s.innerHTML="";const r=n(s,!1,s instanceof SVGElement);return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),r},t})((0,fr.Z)(xr,[["render",function(e,t,n,s,o,r){const i=Nn("message"),l=Nn("suggestions");return Os(),Ns("div",nr,[Us("div",{class:G(["chat-window",{animated:!e.showWindow&&e.hideAnimated,hidden:!e.showWindow}]),style:B(e.style)},[Us("div",sr,[(Os(!0),Ns(Ss,null,Ln(e.filteredMessages,(t=>(Os(),$s(i,{templates:e.templates,multiline:t.multiline,args:t.args,params:t.params,color:t.color,template:t.template,"template-id":t.templateId,key:t.id},null,8,["templates","multiline","args","params","color","template","template-id"])))),128))],512)],6),Us("div",or,[tn(Us("div",rr,[Us("span",{class:G(["prefix",{any:e.modes.length>1}]),style:B({color:e.modeColor})},Z(e.modePrefix),7),tn(Us("textarea",{"onUpdate:modelValue":t[0]||(t[0]=t=>e.message=t),ref:"input",type:"text",autofocus:"",spellcheck:"false",rows:"1",onKeyup:[t[1]||(t[1]=Xo(((...t)=>e.hideInput&&e.hideInput(...t)),["esc"])),t[2]||(t[2]=(...t)=>e.keyUp&&e.keyUp(...t))],onKeydown:t[3]||(t[3]=(...t)=>e.keyDown&&e.keyDown(...t)),onKeypress:t[4]||(t[4]=Xo((a=(...t)=>e.send&&e.send(...t),c=["prevent"],(e,...t)=>{for(let t=0;t<c.length;t++){const n=Zo[c[t]];if(n&&n(e,c))return}return a(e,...t)}),["enter"]))},"\r\n          ",544),[[Ko,e.message]])],512),[[Qo,e.showInput]]),Bs(l,{message:e.message,suggestions:e.suggestions},null,8,["message","suggestions"]),tn(Us("div",{class:"chat-hide-state"},Z(e.hideStateString),513),[[Qo,e.showHideState]])])]);var a,c}]])).mount("#app")})()})();