<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CitizenFX.Core</name>
    </assembly>
    <members>
        <member name="F:CitizenFX.Core.Binding.None">
            <summary>
            No one can call this
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Binding.Local">
            <summary>
            Server only accepts server calls, client only client calls
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Binding.Remote">
            <summary>
            Server only accepts client calls, client only server calls
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Binding.All">
            <summary>
            Accept all incoming calls
            </summary>
        </member>
        <member name="T:CitizenFX.Core.TickAttribute">
            <summary>
            Schedule this method to run on the first frame when this <see cref="T:CitizenFX.Core.BaseScript"/> is loaded
            </summary>
            <remarks>Only works on <see cref="T:CitizenFX.Core.BaseScript"/> inherited class methods</remarks>
        </member>
        <member name="T:CitizenFX.Core.CommandAttribute">
            <summary>
            Register this method to listen for the given <see cref="P:CitizenFX.Core.CommandAttribute.Command"/> when this <see cref="T:CitizenFX.Core.BaseScript"/> is loaded
            </summary>
            <remarks>Only works on <see cref="T:CitizenFX.Core.BaseScript"/> inherited class methods</remarks>
        </member>
        <member name="P:CitizenFX.Core.CommandAttribute.RemapParameters">
            <returns></returns>
            <inheritdoc cref="M:CitizenFX.Core.Func.ConstructCommandRemapped(System.Object,System.Reflection.MethodInfo)"/>
        </member>
        <member name="T:CitizenFX.Core.KeyMapAttribute">
            <summary>Does nothing on server side</summary>
        </member>
        <member name="P:CitizenFX.Core.KeyMapAttribute.RemapParameters">
            <returns></returns>
            <inheritdoc cref="M:CitizenFX.Core.Func.ConstructCommandRemapped(System.Object,System.Reflection.MethodInfo)"/>
        </member>
        <member name="M:CitizenFX.Core.KeyMapAttribute.#ctor(System.String,System.String,System.String,System.String)">
            <inheritdoc cref="T:CitizenFX.Core.KeyMapAttribute"/>
            <param name="command">The command to execute, and the identifier of the binding</param>
            <param name="description">A description for in the settings menu</param>
            <param name="inputMapper">The mapper ID to use for the default binding, e.g. keyboard</param>
            <param name="inputParameter">The IO parameter ID to use for the default binding, e.g. f3</param>
        </member>
        <member name="M:CitizenFX.Core.KeyMapAttribute.#ctor(System.String)">
            <inheritdoc cref="T:CitizenFX.Core.KeyMapAttribute"/>
            <remarks>Does not register the key mapping, so it works the same as <see cref="P:CitizenFX.Core.KeyMapAttribute.Command"/></remarks>
            <param name="commandOnly">The command to execute, and the identifier of the binding</param>
        </member>
        <member name="T:CitizenFX.Core.EventHandlerAttribute">
            <summary>
            Register this method to listen for the given <see cref="P:CitizenFX.Core.EventHandlerAttribute.Event"/> when this <see cref="T:CitizenFX.Core.BaseScript"/> is loaded
            </summary>
            <remarks>Only works on <see cref="T:CitizenFX.Core.BaseScript"/> inherited class methods</remarks>
        </member>
        <member name="T:CitizenFX.Core.ExportAttribute">
            <summary>
            Register this method to listen for the given <see cref="P:CitizenFX.Core.ExportAttribute.Export"/> when this <see cref="T:CitizenFX.Core.BaseScript"/> is loaded
            </summary>
            <remarks>Only works on <see cref="T:CitizenFX.Core.BaseScript"/> inherited class methods</remarks>
        </member>
        <member name="T:CitizenFX.Core.SourceAttribute">
            <summary>
            When used in events it'll be filled with the caller (source) of this event
            </summary>
            <example>
            	Shared libraries
            	<code>[Source] Remote remote</code>
            </example>
            <example>
            	Server libraries
            	<code>[Source] Player player</code>
            </example>
            <example>
            	Shared libraries
            	<code>[Source] bool isRemote</code>
            </example>
        </member>
        <member name="T:CitizenFX.Core.EnableOnLoadAttribute">
            <summary>
            Explicitly enable or disable <see cref="T:CitizenFX.Core.BaseScript" /> instantiation when this resource is starting.
            </summary>
        </member>
        <member name="E:CitizenFX.Core.BaseScript.Tick">
            <summary>
            A method to be scheduled on every game tick, do note that they'll only be rescheduled for the next frame once the method returns.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.BaseScript.Enable">
            <summary>
            Enables all ticks, commands, events, and exports
            </summary>
        </member>
        <member name="M:CitizenFX.Core.BaseScript.Disable">
            <summary>
            Disables all tick repeats, commands, events, and exports
            </summary>
            <remarks>
            1. This <see cref="T:CitizenFX.Core.BaseScript"/> can't re-enable itself except for callbacks, you may want to hold a reference to it.<br />
            2. External code/scripts can still call in for commands, but they'll get <see langword="null"/> returned automatically.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.BaseScript.Disable(System.Boolean)">
            <summary>
            Disables all tick repeats, commands, events, and exports
            </summary>
            <remarks>
            1. This <see cref="T:CitizenFX.Core.BaseScript"/> can't re-enable itself, unless <paramref name="deleteAllCallbacks"/> is <see langword="false" /> then callbacks are still able to, you may want to hold a reference to it.<br />
            2. External code/scripts can still call in for commands, but they'll get <see langword="null"/> returned automatically.
            </remarks>
            <param name="deleteAllCallbacks">If enabled will delete all callbacks targeting this <see cref="T:CitizenFX.Core.BaseScript"/> instance</param>
        </member>
        <member name="M:CitizenFX.Core.BaseScript.OnEnable">
            <summary>
            Called when this script got enabled
            </summary>
        </member>
        <member name="M:CitizenFX.Core.BaseScript.OnDisable">
            <summary>
            Called when this script got disabled
            </summary>
        </member>
        <member name="M:CitizenFX.Core.BaseScript.Delay(System.UInt32)">
            <summary>
            Returns a task that will delay scheduling of the current interval function by the passed amount of time.
            </summary>
            <example>
            await Delay(500);
            </example>
            <param name="msecs">The amount of time by which to delay scheduling this interval function.</param>
            <returns>An awaitable task.</returns>
        </member>
        <member name="M:CitizenFX.Core.BaseScript.RegisterScript(CitizenFX.Core.BaseScript)">
            <summary>
            Activates all ticks, events, and exports.
            </summary>
            <param name="script">script to activate</param>
        </member>
        <member name="M:CitizenFX.Core.BaseScript.UnregisterScript(CitizenFX.Core.BaseScript)">
            <summary>
            Deactivates all ticks, events, and exports.
            </summary>
            <param name="script">script to deactivate</param>
        </member>
        <member name="T:CitizenFX.Core.ClientScript">
            <inheritdoc cref="T:CitizenFX.Core.BaseScript"/>
            <remarks>Will and can only be activated in client environments</remarks>
        </member>
        <member name="T:CitizenFX.Core.ServerScript">
            <inheritdoc cref="T:CitizenFX.Core.BaseScript"/>
            <remarks>Will and can only be activated in server environments</remarks>
        </member>
        <member name="M:CitizenFX.Core.CancelerToken.CancelOrThrowIfRequested">
            <summary>
            Combination of <see cref="P:CitizenFX.Core.CancelerToken.IsCanceled"/> and <see cref="P:CitizenFX.Core.CancelerToken.ThrowOnCancelation"/> for throwing support
            </summary>
            <returns><see langword="true"/> if we need to stop execution of the coroutine otherwise <see langword="false"/></returns>
            <exception cref="T:CitizenFX.Core.CoroutineCanceledException"></exception>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.ContinueWith(System.Action{CitizenFX.Core.Coroutine})">
            <summary>
            Adds an action that will be called when this coroutine is done
            </summary>
            <param name="action">Action to call when coroutine completes</param>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.ContinueWith(System.Action)">
            <summary>
            Adds an action that will be called when this coroutine is done
            </summary>
            <param name="action">Action to call when coroutine completes</param>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.ClearContinueWith">
            <summary>
            Removes all continued actions, e.g.: <see cref="M:CitizenFX.Core.Coroutine.ContinueWith(System.Action{CitizenFX.Core.Coroutine})"/> and sets one that writes any exception thrown instead
            </summary>
            <remarks>Unsafe. Should only be used when you know the exact state of this coroutine</remarks>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Yield">
            <summary>
            Await to stall execution and continue next frame.
            </summary>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.WaitUntil(CitizenFX.Core.TimePoint)">
            <summary>
            Await to stall execution of the current async method and continue after the given time.
            </summary>
            <param name="time">Time in milliseconds after we want to continue execution</param>
            <remarks>Current time can be retrieved by using <see cref="P:CitizenFX.Core.Scheduler.CurrentTime"/></remarks>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.WaitUntil(CitizenFX.Core.TimePoint,CitizenFX.Core.CancelerToken)">
            <summary>
            Await to stall execution of the current async method and continue after the given time.
            </summary>
            <param name="time">Time in milliseconds after we want to continue execution</param>
            <param name="cancelerToken">Canceling token, can be used to stop early</param>
            <remarks>Current time can be retrieved by using <see cref="P:CitizenFX.Core.Scheduler.CurrentTime"/></remarks>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Wait(System.UInt64)">
            <summary>
            Await to stall execution of the current async method and continue after the given delay.
            </summary>
            <param name="delay">Delay in milliseconds after we want to continue execution</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Wait(System.UInt64,CitizenFX.Core.CancelerToken)">
            <summary>
            Await to stall execution of the current async method and continue after the given delay.
            </summary>
            <param name="delay">Delay in milliseconds after we want to continue execution</param>
            <param name="cancelerToken">Canceling token, can be used to stop early</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Delay(System.UInt64)">
            <summary>
            Await to stall execution of the current async method and continue after the given delay.
            </summary>
            <param name="delay">Delay in milliseconds after we want to continue execution</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Delay(System.UInt64,CitizenFX.Core.CancelerToken)">
            <summary>
            Await to stall execution of the current async method and continue after the given delay.
            </summary>
            <param name="delay">Delay in milliseconds after we want to continue execution</param>
            <param name="cancelerToken">Canceling token, can be used to stop early</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)">
            <summary>
            Schedules a delegate to be run at a later time
            </summary>
            <param name="function">Delegate to run</param>
            <param name="delay">Delay until the <paramref name="function"/> is executed</param>
            <param name="iterations">Amount of times we want to run the <paramref name="function"/>. <see cref="F:CitizenFX.Core.Repeat.Infinite"/> or <see cref="F:System.UInt64.MaxValue"/> to infinitely loop.</param>
            <param name="cancelerToken">Allows the cancellation of the given <paramref name="function"/> before it got executed</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule``1(System.Func{CitizenFX.Core.Coroutine{``0}},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},CitizenFX.Core.TimePoint,CitizenFX.Core.CancelerToken)">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule``1(System.Func{CitizenFX.Core.Coroutine{``0}},CitizenFX.Core.TimePoint,CitizenFX.Core.CancelerToken)">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine})">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,CitizenFX.Core.CancelerToken)">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule``1(System.Func{CitizenFX.Core.Coroutine{``0}},System.UInt64,CitizenFX.Core.CancelerToken)">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule(System.Action)">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule(System.Action,System.UInt64,CitizenFX.Core.CancelerToken)">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Schedule(System.Action,System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Schedule(System.Func{CitizenFX.Core.Coroutine},System.UInt64,System.UInt64,CitizenFX.Core.CancelerToken)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Run(System.Action)">
            <summary>
            Runs given function on the main thread; will be scheduled if we're not on the main thread, otherwise it'll be run directly.
            </summary>
            <param name="function">Function to run</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Run(System.Func{CitizenFX.Core.Coroutine})">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Run(System.Action)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.Run``1(System.Func{CitizenFX.Core.Coroutine{``0}})">
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine`1"/></returns>
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.Run(System.Action)"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.RunNextFrame(System.Func{CitizenFX.Core.Coroutine})">
            <summary>
            Runs given function next frame and allows the caller to await it.
            </summary>
            <param name="function">Function to run</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.RunNextFrame(System.Action)">
            <inheritdoc cref="M:CitizenFX.Core.Coroutine.RunNextFrame(System.Func{CitizenFX.Core.Coroutine})"/>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.RunNextFrame``1(System.Func{CitizenFX.Core.Coroutine{``0}})">
            <summary>
            Runs given function directly and allows the caller to await it.
            </summary>
            <param name="function">Function to run</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine`1"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.WhenAny(CitizenFX.Core.Coroutine[])">
            <summary>
            Run all functions and await the first one to complete
            </summary>
            <remarks>Will run the coroutines directly</remarks>
            <param name="coroutines">Coroutines to await</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.WhenAny``1(CitizenFX.Core.Coroutine{``0}[])">
            <summary>
            Run all functions and await the first one to complete
            </summary>
            <remarks>Will run the coroutines directly</remarks>
            <param name="coroutines">Coroutines to await</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.WhenAll(CitizenFX.Core.Coroutine[])">
            <summary>
            Run all functions and await all to complete
            </summary>
            <param name="coroutines">Coroutines to await</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Coroutine.WhenAll``1(CitizenFX.Core.Coroutine{``0}[])">
            <summary>
            Run all functions and await all to complete
            </summary>
            <param name="coroutines">Coroutines to await</param>
            <returns>Awaitable <see cref="T:CitizenFX.Core.Coroutine"/></returns>
        </member>
        <member name="P:CitizenFX.Core.Scheduler.CurrentTime">
            <summary>
            Current time of this scheduler
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Scheduler.MainThread">
            <summary>
            Thread onto which all coroutines will run and be put back to
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Scheduler.s_queue">
            <summary>
            Current time of the scheduler, set per frame.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Scheduler.s_nextFrame">
            <summary>
            Double buffered array for non-ordered scheduling
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Scheduler.Schedule(System.Action)">
            <summary>
            Schedule an action to be run on the next frame
            </summary>
            <param name="coroutine">Action to execute</param>
        </member>
        <member name="M:CitizenFX.Core.Scheduler.Schedule(System.Action,System.UInt64)">
            <summary>
            Schedule an action to be run at the first frame at or after the specified time
            </summary>
            <param name="coroutine">Action to execute</param>
            <param name="delay">Time when it should be executed, see <see cref="P:CitizenFX.Core.Scheduler.CurrentTime"/></param>
        </member>
        <member name="M:CitizenFX.Core.Scheduler.Schedule(System.Action,CitizenFX.Core.TimePoint)">
            <summary>
            Schedule an action to be run at the first frame at or after the specified time
            </summary>
            <param name="coroutine">Action to execute</param>
            <param name="time">Time when it should be executed, see <see cref="P:CitizenFX.Core.Scheduler.CurrentTime"/></param>
        </member>
        <member name="M:CitizenFX.Core.Scheduler.Unschedule(System.Action,CitizenFX.Core.TimePoint)">
            <summary>
            Removes scheduled coroutine from the scheduler (except for the next frame list)
            </summary>
            <param name="coroutine">Coroutine to remove</param>
            <param name="time">Max scheduled time we consider searching to</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:CitizenFX.Core.Scheduler.Update">
            <summary>
            Execute all scheduled coroutines
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Scheduler.NextTaskTime">
            <summary>
            Time in milliseconds when the next action needs to be activated
            </summary>
            <returns>Delay in milliseconds or ~0ul (<see cref="F:System.UInt64.MaxValue"/>) if there's nothing to run</returns>
        </member>
        <member name="T:CitizenFX.Core.TimePoint">
            <summary>
            Absolute time for scheduling on fixed points in time
            </summary>
        </member>
        <member name="F:CitizenFX.Core.DynFuncExceptions.None">
            <summary>
            Ignore any exception thrown
            /// </summary>
        </member>
        <member name="F:CitizenFX.Core.DynFuncExceptions.InvalidCastException">
            <summary>
            If you like to keep the option to ignore calls with incorrect arguments but d
            </summary>
            <example>
            // Disable InvalidCastException logs but still make use of them being ignored
            Debug.LogExceptionsOnDynFunc &amp;= ~DynFuncExceptions.InvalidCastException;
            </example>
        </member>
        <member name="F:CitizenFX.Core.DynFuncExceptions.ReportTypeMatching">
            <summary>
            Will output parameters vs argument conversion/castability report, helpful for development
            </summary>
            <remarks>Can have a performance if it's hit often.</remarks>
        </member>
        <member name="F:CitizenFX.Core.DynFuncExceptions.Everything">
            <summary>
            Panic mode, give all details
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Func.Create(System.Type,System.String)">
            <summary>
            Creates a new dynamic invokable function
            </summary>
            <param name="type">the type which should contain the method</param>
            <param name="method">method name to find and use in type's method list</param>
            <returns>dynamically invokable delegate</returns>
            <exception cref="T:System.ArgumentNullException">when given target or method is null</exception>
            <exception cref="T:System.ArgumentException">when the method could not be found in target's method list</exception>
        </member>
        <member name="M:CitizenFX.Core.Func.Create(System.Object,System.String)">
            <summary>
            Creates a new dynamic invokable function
            </summary>
            <param name="target">the instance object</param>
            <param name="method">method name to find and use in target's method list</param>
            <returns>dynamically invokable delegate</returns>
            <exception cref="T:System.ArgumentNullException">when given target or method is null</exception>
            <exception cref="T:System.ArgumentException">when the method could not be found in target's method list</exception>
        </member>
        <member name="M:CitizenFX.Core.Func.Create(System.Object,System.Reflection.MethodInfo)">
            <summary>
            Creates a new dynamic invokable function
            </summary>
            <param name="target">the instance object, may be null for static methods</param>
            <param name="method">method to make dynamically invokable</param>
            <returns>dynamically invokable delegate</returns>
            <exception cref="T:System.ArgumentNullException">when the given method is non-static and target is null or if given method is null</exception>
        </member>
        <member name="M:CitizenFX.Core.Func.Create(System.Delegate)">
            <summary>
            Creates a dynamic invokable function or simply returns it
            </summary>
            <param name="deleg">delegate to make dynamically invokable</param>
            <returns>dynamically invokable delegate or returns <paramref name="deleg"/> if it's aleady of type <see cref="T:CitizenFX.Core.DynFunc"/></returns>
            <exception cref="T:System.ArgumentNullException">when the given method is non-static and target is null or if given method is null</exception>
        </member>
        <member name="M:CitizenFX.Core.Func.ConstructCommandRemapped(System.Object,System.Reflection.MethodInfo)">
            <summary>
            If enabled creates a <see cref="T:CitizenFX.Core.DynFunc"/> that remaps input (<see cref="T:System.UInt16"/> source, <see cref="T:System.Object"/>[] arguments, <see cref="T:System.String"/> raw) to known types:<br />
            <b>source</b>: <see cref="T:System.UInt16"/>, <see cref="T:System.UInt32"/>, <see cref="T:System.Int32"/>, <see cref="T:System.Boolean"/>, <see cref="T:CitizenFX.Core.Remote"/>, or any type constructable from <see cref="T:CitizenFX.Core.Remote"/> including Player types.<br />
            <b>arguments</b>: <see cref="T:System.Object"/>[] or <see cref="T:System.String"/>[].<br />
            <b>raw</b>: <see cref="T:System.String"/>
            </summary>
            <param name="target">Method's associated instance</param>
            <param name="method">Method to wrap</param>
            <returns>Dynamic invocable <see cref="T:CitizenFX.Core.DynFunc"/> with remapping and conversion support.</returns>
            <exception cref="T:System.ArgumentException">When <see cref="T:CitizenFX.Core.SourceAttribute"/> is used on a non supported type.</exception>
            <exception cref="T:System.Reflection.TargetParameterCountException">When any requested parameter isn't supported.</exception>
        </member>
        <member name="M:CitizenFX.Core.Func.GetWrappedMethod(CitizenFX.Core.DynFunc)">
            <summary>
            Returns the original method that's been wrapped or its own <see cref="P:System.Delegate.Method"/>
            </summary>
            <param name="dynFunc">this reference</param>
            <returns>The wrapped method or <see cref="P:System.Delegate.Method"/> if it wasn't wrapped</returns>
        </member>
        <member name="M:CitizenFX.Core.Events.RegisterEventHandler(System.String,CitizenFX.Core.DynFunc,CitizenFX.Core.Binding)">
            <summary>
            Register an event handler
            </summary>
            <remarks>Be aware this will keep <paramref name="handler"/>.Target object alive once registered, unless it's of type <see cref="T:CitizenFX.Core.BaseScript"/></remarks>
            <param name="eventName">name to listen for</param>
            <param name="handler">delegate to call once triggered</param>
            <param name="binding">limit calls to certain sources, e.g.: server only, client only</param>
        </member>
        <member name="M:CitizenFX.Core.Events.UnregisterEventHandler(System.String,CitizenFX.Core.DynFunc)">
            <summary>
            Unregister an event handler
            </summary>
            <param name="eventName">name to remove event for</param>
            <param name="handler">delegate to remove</param>
        </member>
        <member name="M:CitizenFX.Core.Events.TriggerAllClientsEvent(System.String,System.Object[])">
            <summary>
            Broadcasts an event to all connected players.
            </summary>
            <param name="eventName">The name of the event.</param>
            <param name="args">Arguments to pass to the event.</param>
        </member>
        <member name="M:CitizenFX.Core.Events.TriggerLatentAllClientsEvent(System.String,System.Int32,System.Object[])">
            <summary>
            Broadcasts an event to all connected players.
            </summary>
            <param name="eventName">The name of the event.</param>
            <param name="bytesPerSecond">Amount of bytes send in 1 second.</param>
            <param name="args">Arguments to pass to the event.</param>
        </member>
        <member name="M:CitizenFX.Core.EventHandler.Enable">
            <summary>
            Should only be called by <see cref="M:CitizenFX.Core.BaseScript.Enable"/> or any other code that guarantees that it is only called once
            </summary>
        </member>
        <member name="M:CitizenFX.Core.EventHandlerSet.Add(CitizenFX.Core.DynFunc,CitizenFX.Core.Binding)">
            <summary>
            Register an event handler
            </summary>
            <param name="deleg">delegate to call once triggered</param>
            <param name="binding">limit calls to certain sources, e.g.: server only, client only</param>
        </member>
        <member name="M:CitizenFX.Core.EventHandlerSet.Remove(System.Delegate)">
            <summary>
            Unregister an event handler
            </summary>
            <param name="deleg">delegate to remove</param>
        </member>
        <member name="M:CitizenFX.Core.EventHandlerSet.op_Addition(CitizenFX.Core.EventHandlerSet,CitizenFX.Core.DynFunc)">
            <summary>
            Register an event handler
            </summary>
            <remarks>Will add it as <see cref="F:CitizenFX.Core.Binding.Local"/>, use <see cref="M:CitizenFX.Core.EventHandlerSet.Add(CitizenFX.Core.DynFunc,CitizenFX.Core.Binding)"/> to explicitly set the binding.</remarks>
            <param name="entry">this event handler set</param>
            <param name="deleg">delegate to register</param>
            <returns>itself</returns>
        </member>
        <member name="M:CitizenFX.Core.EventHandlerSet.op_Subtraction(CitizenFX.Core.EventHandlerSet,CitizenFX.Core.DynFunc)">
            <summary>
            Unregister an event handler
            </summary>
            <param name="entry">this event handler set</param>
            <param name="deleg">delegate to register</param>
            <returns>itself</returns>
        </member>
        <member name="M:CitizenFX.Core.EventHandlerSet.op_Addition(CitizenFX.Core.EventHandlerSet,System.Delegate)">
            <summary>
            Register an event handler
            </summary>
            <remarks>Will add it as <see cref="F:CitizenFX.Core.Binding.Local"/>, use <see cref="M:CitizenFX.Core.EventHandlerSet.Add(CitizenFX.Core.DynFunc,CitizenFX.Core.Binding)"/> to explicitly set the binding.</remarks>
            <param name="entry">this event handler set</param>
            <param name="deleg">delegate to register</param>
            <returns>itself</returns>
        </member>
        <member name="M:CitizenFX.Core.EventHandlerSet.op_Subtraction(CitizenFX.Core.EventHandlerSet,System.Delegate)">
            <summary>
            Unregister an event handler
            </summary>
            <param name="entry">this event handler set</param>
            <param name="deleg">delegate to register</param>
            <returns>itself</returns>
        </member>
        <member name="M:CitizenFX.Core.EventHandlerSet.Enable">
            <summary>
            Should only be called by <see cref="M:CitizenFX.Core.EventHandler.Enable"/> or any other code that guarantees that it is only called once
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Exports.Enable">
            <summary>
            Should only be called by <see cref="M:CitizenFX.Core.BaseScript.Enable"/> or any other code that guarantees that it is only called once
            </summary>
        </member>
        <member name="M:CitizenFX.Core.MsgPackDeserializer.DeserializeArray(System.Byte*,System.Int64,System.String)">
            <summary>
            Starts deserialization from an array type
            </summary>
            <param name="data">ptr to byte data</param>
            <param name="size">size of byte data</param>
            <param name="netSource">from whom came this?</param>
            <returns>arguments that can be passed into dynamic delegates</returns>
        </member>
        <member name="M:CitizenFX.Core.MsgPackDeserializer.ReadSingleLE">
            <summary>
            Read a <see cref="T:System.Single"/> stored as little endian, used for custom vectors
            </summary>
        </member>
        <member name="M:CitizenFX.Core.MsgPackSerializer.SerializeCoTaskMemory(System.Object,System.Boolean)">
            <summary>
            Experimental, uses the CoTask memory instead of a managed byte[]
            </summary>
            <param name="obj"></param>
            <param name="remote"></param>
            <returns></returns>
        </member>
        <member name="T:CitizenFX.Core.CoTaskMemoryStream">
            <summary>
            Experimental memory stream
            </summary>
        </member>
        <member name="M:CitizenFX.Core.ReferenceFunctionManager.Create(CitizenFX.Core.DynFunc)">
            <summary>
            Register a delegate to other runtimes and/or our host (reference function)
            </summary>
            <param name="method">Delagate to register to the external world</param>
            <returns>( internalReferenceId, externalReferenceId )</returns>
            <remarks>Don't alter the returned value</remarks>
        </member>
        <member name="M:CitizenFX.Core.ReferenceFunctionManager.Remove(System.Int32)">
            <summary>
            Remove reference function by id
            </summary>
            <param name="referenceId">Internal reference id of the reference to remove</param>
        </member>
        <member name="M:CitizenFX.Core.ReferenceFunctionManager.RemoveAllWithTarget(System.Object)">
            <summary>
            Remove all reference functions that are targeting a specific object
            </summary>
            <remarks>Slow, may need to be replaced</remarks>
            <param name="target"></param>
        </member>
        <member name="M:CitizenFX.Core.ReferenceFunctionManager.SetDelegate(System.Int32,CitizenFX.Core.DynFunc)">
            <summary>
            Set reference function to another delegate
            </summary>
            <param name="referenceId">Reference id of the reference to remove</param>
            <param name="newFunc">New delegate/method to set the reference function to</param>
            <returns><see langword="true"/> if found and changed, <see langword="false"/> otherwise</returns>
        </member>
        <member name="T:CitizenFX.Core.CString">
            <summary>
            A null-terminated byte string. Dedicated to interop with native code, equivalent to a (non-wide) c-string.
            </summary>
            <remarks>In general don't use this type where you would normally use <see cref="T:System.String"/> in your .NET code, it needs to do conversion between UTF16 and UTF8 and is therefore slower.
            This type and its conversions are more performant in regards to interop with native code (C++), and other runtimes, for these cases you may want to use this.</remarks>
        </member>
        <member name="P:CitizenFX.Core.CString.Empty">
            <summary>
            An empty <see cref="T:CitizenFX.Core.CString"/> equivalent to <see cref="F:System.String.Empty"/>, null terminated for interop.
            </summary>				
        </member>
        <member name="M:CitizenFX.Core.CString.Create(System.Byte*)">
            <summary>
            CString with a copy of str.
            </summary>
            <returns>a null terminated CString or null if parameter str == null</returns>
        </member>
        <member name="M:CitizenFX.Core.CString.Create(System.Byte*,System.UInt32)">
            <summary>
            CString with a copy of str, limited to given length.
            </summary>
            <returns>a null terminated CString or null if parameter str == null</returns>
        </member>
        <member name="P:CitizenFX.Core.CString.Length">
            <summary>
            Get the length of this string, excluding the null terminator
            </summary>
        </member>
        <member name="P:CitizenFX.Core.CString.LongLength">
            <summary>
            Get the length in long form of this string, excluding the null terminator
            </summary>
        </member>
        <member name="P:CitizenFX.Core.CString.Item(System.Int32)">
            <summary>
            Get the byte at the provided index
            </summary>
            <param name="index">the position we'd like to retrieve the byte from</param>
            <returns>byte representing an ASCII or UTF8 (biggest bit set) character</returns>
        </member>
        <member name="P:CitizenFX.Core.CString.Item(System.UInt32)">
            <summary>
            Get the byte at the provided index
            </summary>
            <param name="index">the position we'd like to retrieve the byte from</param>
            <returns>byte representing an ASCII or UTF8 (biggest bit set) character</returns>
        </member>
        <member name="M:CitizenFX.Core.CString.Substring(System.Int32)">
            <summary>
            Retrieves a substring from this instance. The substring starts at a specified character position.
            </summary>
            <param name="startIndex">start character index</param>
            <returns>CString with the requested part of characters of this string</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="startIndex"/> &lt; 0 or &gt;= Length</exception>
        </member>
        <member name="M:CitizenFX.Core.CString.Substring(System.UInt32)">
            <summary>
            Retrieves a substring from this instance. The substring starts at a specified character position.
            Unsigned version, requires less checks for the same result.
            </summary>
            <param name="startIndex">start character index</param>
            <returns>CString with the requested part of characters of this string</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="startIndex"/> &lt; 0 or &gt;= Length</exception>
        </member>
        <member name="M:CitizenFX.Core.CString.Substring(System.Int32,System.Int32)">
            <summary>
            Retrieves a substring from this instance. The substring starts at a specified character position and has a specified length.
            </summary>
            <param name="startIndex">start character index</param>
            <param name="length">amount of characters we want in total, can be less. -1 will retrieve all</param>
            <returns>CString with the requested part of characters of this string</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="startIndex"/> &gt;= Length</exception>
        </member>
        <member name="M:CitizenFX.Core.CString.Substring(System.UInt32,System.UInt32)">
            <summary>
            Retrieves a substring from this instance. The substring starts at a specified character position and has a specified length.
            Unsigned version, requires less checks for the same result.
            </summary>
            <param name="startIndex">start character index</param>
            <param name="length">amount of characters we want in total, can be less.</param>
            <returns>CString with the requested part of characters of this string</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="startIndex"/> &lt; 0 or &gt;= Length</exception>
        </member>
        <member name="M:CitizenFX.Core.CString.Concat(CitizenFX.Core.CString[])">
            <summary>
            Joins/merges all given strings
            </summary>
            <param name="strings">Strings to join</param>
            <returns><see cref="T:CitizenFX.Core.CString"/> containing all given strings concatenated</returns>
        </member>
        <member name="M:CitizenFX.Core.CString.ToString">
            <summary>
            Converts to a UTF16 string
            </summary>
            <returns>UTF16 string</returns>
        </member>
        <member name="M:CitizenFX.Core.CString.ToCString(System.String)">
            <summary>
            Converts a UTF16 into a UTF8/ASCII string
            </summary>
            <param name="str">UTF16 string to convert</param>
            <returns>UTF8 string</returns>
        </member>
        <member name="M:CitizenFX.Core.CString.op_Implicit(System.String)~CitizenFX.Core.CString">
            <summary>
            Convert a C# string into a null-terminated c-string
            </summary>
            <param name="str">UTF16 encoded string (default C#)</param>
        </member>
        <member name="M:CitizenFX.Core.CString.op_Explicit(CitizenFX.Core.CString)~System.String">
            <summary>
            Convert a null-terminated c-string into a C# string
            </summary>
            <param name="str">null-terminated c-string</param>
        </member>
        <member name="M:CitizenFX.Core.CString.op_Implicit(CitizenFX.Core.OutString)~CitizenFX.Core.CString">
            <summary>
            Copy <see cref="T:CitizenFX.Core.OutString"/> into a null-terminated c-string
            </summary>
            <param name="str">OutString to copy</param>
            <!-- Does put a dependency on OutString -->
        </member>
        <member name="M:CitizenFX.Core.CString.CompareASCII(System.String)">
            <summary>
            Compares a CString to a C# string only considering ASCII characters (7 bits)
            any non 7 bit characters on either side will return false
            </summary>
            <param name="str">string to compare</param>
            <returns>true if both sides have equivalent characters in the ASCII character space</returns>
        </member>
        <member name="M:CitizenFX.Core.CString.CompareASCII(CitizenFX.Core.CString,System.String)">
            <summary>
            Compares a CString to a C# string only considering ASCII characters (7 bits)
            any non 7 bit characters on either side will return false
            </summary>
            <param name="left">CString to compare</param>
            <param name="right">string to compare</param>
            <returns>true if both sides have equivalent characters in the ASCII character space</returns>
        </member>
        <member name="M:CitizenFX.Core.CString.ToASCII(System.String,System.Byte)">
            <summary>
            Converts a C# string to a CString only accepting ASCII characters (7 bits)
            any non 7 bit character will be become the given <paramref name="invalid"/> value
            </summary>
            <param name="str">string to convert</param>
            <param name="invalid">character to insert in case of a non-7 bit character</param>
            <returns>CString with only ASCII (7 bit) characters, or null if <paramref name="str"/> == null</returns>
        </member>
        <member name="M:CitizenFX.Core.CString.GetPinnableReference">
            <summary>
            allows us to directly use `fixed(byte* ptr = cstring)`
            </summary>
            <returns>reference to first byte</returns>
        </member>
        <member name="F:CitizenFX.Core.MathUtil.ZeroTolerance">
            <summary>
            The value for which all absolute numbers smaller than are considered equal to zero.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.MathUtil.Pi">
            <summary>
            A value specifying the approximation of π which is 180 degrees.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.MathUtil.TwoPi">
            <summary>
            A value specifying the approximation of 2π which is 360 degrees.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.MathUtil.Tau">
            <summary>
            Mathematical term for 2π, which is equivalent to 360 degrees.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.MathUtil.PiOverTwo">
            <summary>
            A value specifying the approximation of π/2 which is 90 degrees.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.MathUtil.PiOverFour">
            <summary>
            A value specifying the approximation of π/4 which is 45 degrees.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.NearEqual(System.Single,System.Single)">
            <summary>
            Checks if a and b are almost equals, taking into account the magnitude of floating point numbers (unlike <see cref="M:CitizenFX.Core.MathUtil.WithinEpsilon(System.Single,System.Single,System.Single)"/> method). See Remarks.
            See remarks.
            </summary>
            <param name="a">The left value to compare.</param>
            <param name="b">The right value to compare.</param>
            <returns><c>true</c> if a almost equal to b, <c>false</c> otherwise</returns>
            <remarks>
            The code is using the technique described by Bruce Dawson in 
            <a href="http://randomascii.wordpress.com/2012/02/25/comparing-floating-point-numbers-2012-edition/">Comparing Floating point numbers 2012 edition</a>. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.IsZero(System.Single)">
            <summary>
            Determines whether the specified value is close to zero (0.0f).
            </summary>
            <param name="a">The floating value.</param>
            <returns><c>true</c> if the specified value is close to zero (0.0f); otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.IsOne(System.Single)">
            <summary>
            Determines whether the specified value is close to one (1.0f).
            </summary>
            <param name="a">The floating value.</param>
            <returns><c>true</c> if the specified value is close to one (1.0f); otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.WithinEpsilon(System.Single,System.Single,System.Single)">
            <summary>
            Checks if a - b are almost equals within a float epsilon.
            </summary>
            <param name="a">The left value to compare.</param>
            <param name="b">The right value to compare.</param>
            <param name="epsilon">Epsilon value</param>
            <returns><c>true</c> if a almost equal to b within a float epsilon, <c>false</c> otherwise</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.RevolutionsToDegrees(System.Single)">
            <summary>
            Converts revolutions to degrees.
            </summary>
            <param name="revolution">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.RevolutionsToRadians(System.Single)">
            <summary>
            Converts revolutions to radians.
            </summary>
            <param name="revolution">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.RevolutionsToGradians(System.Single)">
            <summary>
            Converts revolutions to gradians.
            </summary>
            <param name="revolution">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.DegreesToRevolutions(System.Single)">
            <summary>
            Converts degrees to revolutions.
            </summary>
            <param name="degree">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.DegreesToRadians(System.Single)">
            <summary>
            Converts degrees to radians.
            </summary>
            <param name="degree">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.RadiansToRevolutions(System.Single)">
            <summary>
            Converts radians to revolutions.
            </summary>
            <param name="radian">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.RadiansToGradians(System.Single)">
            <summary>
            Converts radians to gradians.
            </summary>
            <param name="radian">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.GradiansToRevolutions(System.Single)">
            <summary>
            Converts gradians to revolutions.
            </summary>
            <param name="gradian">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.GradiansToDegrees(System.Single)">
            <summary>
            Converts gradians to degrees.
            </summary>
            <param name="gradian">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.GradiansToRadians(System.Single)">
            <summary>
            Converts gradians to radians.
            </summary>
            <param name="gradian">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.RadiansToDegrees(System.Single)">
            <summary>
            Converts radians to degrees.
            </summary>
            <param name="radian">The value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Clamp(System.Single,System.Single,System.Single)">
            <summary>
            Clamps the specified value.
            </summary>
            <param name="value">The value.</param>
            <param name="min">The min.</param>
            <param name="max">The max.</param>
            <returns>The result of clamping a value between min and max</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Clamp(System.Int32,System.Int32,System.Int32)">
            <summary>
            Clamps the specified value.
            </summary>
            <param name="value">The value.</param>
            <param name="min">The min.</param>
            <param name="max">The max.</param>
            <returns>The result of clamping a value between min and max</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Lerp(System.Double,System.Double,System.Double)">
            <summary>
            Interpolates between two values using a linear function by a given amount.
            </summary>
            <remarks>
            See http://www.encyclopediaofmath.org/index.php/Linear_interpolation and
            http://fgiesen.wordpress.com/2012/08/15/linear-interpolation-past-present-and-future/
            </remarks>
            <param name="from">Value to interpolate from.</param>
            <param name="to">Value to interpolate to.</param>
            <param name="amount">Interpolation amount.</param>
            <returns>The result of linear interpolation of values based on the amount.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Lerp(System.Single,System.Single,System.Single)">
            <summary>
            Interpolates between two values using a linear function by a given amount.
            </summary>
            <remarks>
            See http://www.encyclopediaofmath.org/index.php/Linear_interpolation and
            http://fgiesen.wordpress.com/2012/08/15/linear-interpolation-past-present-and-future/
            </remarks>
            <param name="from">Value to interpolate from.</param>
            <param name="to">Value to interpolate to.</param>
            <param name="amount">Interpolation amount.</param>
            <returns>The result of linear interpolation of values based on the amount.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Lerp(System.Byte,System.Byte,System.Single)">
            <summary>
            Interpolates between two values using a linear function by a given amount.
            </summary>
            <remarks>
            See http://www.encyclopediaofmath.org/index.php/Linear_interpolation and
            http://fgiesen.wordpress.com/2012/08/15/linear-interpolation-past-present-and-future/
            </remarks>
            <param name="from">Value to interpolate from.</param>
            <param name="to">Value to interpolate to.</param>
            <param name="amount">Interpolation amount.</param>
            <returns>The result of linear interpolation of values based on the amount.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.SmoothStep(System.Single)">
            <summary>
            Performs smooth (cubic Hermite) interpolation between 0 and 1.
            </summary>
            <remarks>
            See https://en.wikipedia.org/wiki/Smoothstep
            </remarks>
            <param name="amount">Value between 0 and 1 indicating interpolation amount.</param>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.SmootherStep(System.Single)">
            <summary>
            Performs a smooth(er) interpolation between 0 and 1 with 1st and 2nd order derivatives of zero at endpoints.
            </summary>
            <remarks>
            See https://en.wikipedia.org/wiki/Smoothstep
            </remarks>
            <param name="amount">Value between 0 and 1 indicating interpolation amount.</param>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Mod(System.Single,System.Single)">
            <summary>
            Calculates the modulo of the specified value.
            </summary>
            <param name="value">The value.</param>
            <param name="modulo">The modulo.</param>
            <returns>The result of the modulo applied to value</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Mod2PI(System.Single)">
            <summary>
            Calculates the modulo 2*PI of the specified value.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the modulo applied to value</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Wrap(System.Int32,System.Int32,System.Int32)">
            <summary>
            Wraps the specified value into a range [min, max]
            </summary>
            <param name="value">The value to wrap.</param>
            <param name="min">The min.</param>
            <param name="max">The max.</param>
            <returns>Result of the wrapping.</returns>
            <exception cref="T:System.ArgumentException">Is thrown when <paramref name="min"/> is greater than <paramref name="max"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Wrap(System.Single,System.Single,System.Single)">
            <summary>
            Wraps the specified value into a range [min, max[
            </summary>
            <param name="value">The value.</param>
            <param name="min">The min.</param>
            <param name="max">The max.</param>
            <returns>Result of the wrapping.</returns>
            <exception cref="T:System.ArgumentException">Is thrown when <paramref name="min"/> is greater than <paramref name="max"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Gauss(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Gauss function.
            </summary>
            <param name="amplitude">Curve amplitude.</param>
            <param name="x">Position X.</param>
            <param name="y">Position Y</param>
            <param name="radX">Radius X.</param>
            <param name="radY">Radius Y.</param>
            <param name="sigmaX">Curve sigma X.</param>
            <param name="sigmaY">Curve sigma Y.</param>
            <returns>The result of Gaussian function.</returns>
        </member>
        <member name="M:CitizenFX.Core.MathUtil.Gauss(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Gauss function.
            </summary>
            <param name="amplitude">Curve amplitude.</param>
            <param name="x">Position X.</param>
            <param name="y">Position Y</param>
            <param name="radX">Radius X.</param>
            <param name="radY">Radius Y.</param>
            <param name="sigmaX">Curve sigma X.</param>
            <param name="sigmaY">Curve sigma Y.</param>
            <returns>The result of Gaussian function.</returns>
        </member>
        <member name="T:CitizenFX.Core.Matrix">
            <summary>
            Represents a 4x4 mathematical matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.Zero">
            <summary>
            A <see cref="T:CitizenFX.Core.Matrix"/> with all of its components set to zero.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.Identity">
            <summary>
            The identity <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M11">
            <summary>
            Value at row 1 column 1 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M12">
            <summary>
            Value at row 1 column 2 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M13">
            <summary>
            Value at row 1 column 3 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M14">
            <summary>
            Value at row 1 column 4 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M21">
            <summary>
            Value at row 2 column 1 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M22">
            <summary>
            Value at row 2 column 2 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M23">
            <summary>
            Value at row 2 column 3 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M24">
            <summary>
            Value at row 2 column 4 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M31">
            <summary>
            Value at row 3 column 1 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M32">
            <summary>
            Value at row 3 column 2 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M33">
            <summary>
            Value at row 3 column 3 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M34">
            <summary>
            Value at row 3 column 4 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M41">
            <summary>
            Value at row 4 column 1 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M42">
            <summary>
            Value at row 4 column 2 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M43">
            <summary>
            Value at row 4 column 3 of the matrix.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix.M44">
            <summary>
            Value at row 4 column 4 of the matrix.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Up">
            <summary>
            Gets or sets the up <see cref="T:CitizenFX.Core.Vector3"/> of the matrix; that is M21, M22, and M23.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Down">
            <summary>
            Gets or sets the down <see cref="T:CitizenFX.Core.Vector3"/> of the matrix; that is -M21, -M22, and -M23.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Right">
            <summary>
            Gets or sets the right <see cref="T:CitizenFX.Core.Vector3"/> of the matrix; that is M11, M12, and M13.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Left">
            <summary>
            Gets or sets the left <see cref="T:CitizenFX.Core.Vector3"/> of the matrix; that is -M11, -M12, and -M13.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Forward">
            <summary>
            Gets or sets the forward <see cref="T:CitizenFX.Core.Vector3"/> of the matrix; that is -M31, -M32, and -M33.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Backward">
            <summary>
            Gets or sets the backward <see cref="T:CitizenFX.Core.Vector3"/> of the matrix; that is M31, M32, and M33.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Matrix.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Matrix"/> struct.
            </summary>
            <param name="value">The value that will be assigned to all components.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Matrix"/> struct.
            </summary>
            <param name="M11">The value to assign at row 1 column 1 of the matrix.</param>
            <param name="M12">The value to assign at row 1 column 2 of the matrix.</param>
            <param name="M13">The value to assign at row 1 column 3 of the matrix.</param>
            <param name="M14">The value to assign at row 1 column 4 of the matrix.</param>
            <param name="M21">The value to assign at row 2 column 1 of the matrix.</param>
            <param name="M22">The value to assign at row 2 column 2 of the matrix.</param>
            <param name="M23">The value to assign at row 2 column 3 of the matrix.</param>
            <param name="M24">The value to assign at row 2 column 4 of the matrix.</param>
            <param name="M31">The value to assign at row 3 column 1 of the matrix.</param>
            <param name="M32">The value to assign at row 3 column 2 of the matrix.</param>
            <param name="M33">The value to assign at row 3 column 3 of the matrix.</param>
            <param name="M34">The value to assign at row 3 column 4 of the matrix.</param>
            <param name="M41">The value to assign at row 4 column 1 of the matrix.</param>
            <param name="M42">The value to assign at row 4 column 2 of the matrix.</param>
            <param name="M43">The value to assign at row 4 column 3 of the matrix.</param>
            <param name="M44">The value to assign at row 4 column 4 of the matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.#ctor(System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Matrix"/> struct.
            </summary>
            <param name="values">The values to assign to the components of the matrix. This must be an array with sixteen elements.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="values"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="values"/> contains more or less than sixteen elements.</exception>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Row1">
            <summary>
            Gets or sets the first row in the matrix; that is M11, M12, M13, and M14.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Row2">
            <summary>
            Gets or sets the second row in the matrix; that is M21, M22, M23, and M24.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Row3">
            <summary>
            Gets or sets the third row in the matrix; that is M31, M32, M33, and M34.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Row4">
            <summary>
            Gets or sets the fourth row in the matrix; that is M41, M42, M43, and M44.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Column1">
            <summary>
            Gets or sets the first column in the matrix; that is M11, M21, M31, and M41.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Column2">
            <summary>
            Gets or sets the second column in the matrix; that is M12, M22, M32, and M42.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Column3">
            <summary>
            Gets or sets the third column in the matrix; that is M13, M23, M33, and M43.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Column4">
            <summary>
            Gets or sets the fourth column in the matrix; that is M14, M24, M34, and M44.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.TranslationVector">
            <summary>
            Gets or sets the translation of the matrix; that is M41, M42, and M43.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.ScaleVector">
            <summary>
            Gets or sets the scale of the matrix; that is M11, M22, and M33.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix.IsIdentity">
            <summary>
            Gets a value indicating whether this instance is an identity matrix.
            </summary>
            <value>
            <c>true</c> if this instance is an identity matrix; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Item(System.Int32)">
            <summary>
            Gets or sets the component at the specified index.
            </summary>
            <value>The value of the matrix component, depending on the index.</value>
            <param name="index">The zero-based index of the component to access.</param>
            <returns>The value of the component at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="index"/> is out of the range [0, 15].</exception>
        </member>
        <member name="P:CitizenFX.Core.Matrix.Item(System.Int32,System.Int32)">
            <summary>
            Gets or sets the component at the specified index.
            </summary>
            <value>The value of the matrix component, depending on the index.</value>
            <param name="row">The row of the matrix to access.</param>
            <param name="column">The column of the matrix to access.</param>
            <returns>The value of the component at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="row"/> or <paramref name="column"/>is out of the range [0, 3].</exception>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Determinant">
            <summary>
            Calculates the determinant of the matrix.
            </summary>
            <returns>The determinant of the matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Invert">
            <summary>
            Inverts the matrix.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Transpose">
            <summary>
            Transposes the matrix.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Orthogonalize">
            <summary>
            Orthogonalizes the specified matrix.
            </summary>
            <remarks>
            <para>Orthogonalization is the process of making all rows orthogonal to each other. This
            means that any given row in the matrix will be orthogonal to any other given row in the
            matrix.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting matrix
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the matrix rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Orthonormalize">
            <summary>
            Orthonormalizes the specified matrix.
            </summary>
            <remarks>
            <para>Orthonormalization is the process of making all rows and columns orthogonal to each
            other and making all rows and columns of unit length. This means that any given row will
            be orthogonal to any other given row and any given column will be orthogonal to any other
            given column. Any given row will not be orthogonal to any given column. Every row and every
            column will be of unit length.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting matrix
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the matrix rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.DecomposeQR(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Decomposes a matrix into an orthonormalized matrix Q and a right triangular matrix R.
            </summary>
            <param name="Q">When the method completes, contains the orthonormalized matrix of the decomposition.</param>
            <param name="R">When the method completes, contains the right triangular matrix of the decomposition.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.DecomposeLQ(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Decomposes a matrix into a lower triangular matrix L and an orthonormalized matrix Q.
            </summary>
            <param name="L">When the method completes, contains the lower triangular matrix of the decomposition.</param>
            <param name="Q">When the method completes, contains the orthonormalized matrix of the decomposition.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Decompose(CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector3@)">
            <summary>
            Decomposes a matrix into a scale, rotation, and translation.
            </summary>
            <param name="scale">When the method completes, contains the scaling component of the decomposed matrix.</param>
            <param name="rotation">When the method completes, contains the rotation component of the decomposed matrix.</param>
            <param name="translation">When the method completes, contains the translation component of the decomposed matrix.</param>
            <remarks>
            This method is designed to decompose an SRT transformation matrix only.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.DecomposeUniformScale(System.Single@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector3@)">
            <summary>
            Decomposes a uniform scale matrix into a scale, rotation, and translation.
            A uniform scale matrix has the same scale in every axis.
            </summary>
            <param name="scale">When the method completes, contains the scaling component of the decomposed matrix.</param>
            <param name="rotation">When the method completes, contains the rotation component of the decomposed matrix.</param>
            <param name="translation">When the method completes, contains the translation component of the decomposed matrix.</param>
            <remarks>
            This method is designed to decompose only an SRT transformation matrix that has the same scale in every axis.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.ExchangeRows(System.Int32,System.Int32)">
            <summary>
            Exchanges two rows in the matrix.
            </summary>
            <param name="firstRow">The first row to exchange. This is an index of the row starting at zero.</param>
            <param name="secondRow">The second row to exchange. This is an index of the row starting at zero.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.ExchangeColumns(System.Int32,System.Int32)">
            <summary>
            Exchanges two columns in the matrix.
            </summary>
            <param name="firstColumn">The first column to exchange. This is an index of the column starting at zero.</param>
            <param name="secondColumn">The second column to exchange. This is an index of the column starting at zero.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.ToArray">
            <summary>
            Creates an array containing the elements of the matrix.
            </summary>
            <returns>A sixteen-element array containing the components of the matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Add(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Determines the sum of two matrices.
            </summary>
            <param name="left">The first matrix to add.</param>
            <param name="right">The second matrix to add.</param>
            <param name="result">When the method completes, contains the sum of the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Add(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Determines the sum of two matrices.
            </summary>
            <param name="left">The first matrix to add.</param>
            <param name="right">The second matrix to add.</param>
            <returns>The sum of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Subtract(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Determines the difference between two matrices.
            </summary>
            <param name="left">The first matrix to subtract.</param>
            <param name="right">The second matrix to subtract.</param>
            <param name="result">When the method completes, contains the difference between the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Subtract(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Determines the difference between two matrices.
            </summary>
            <param name="left">The first matrix to subtract.</param>
            <param name="right">The second matrix to subtract.</param>
            <returns>The difference between the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Multiply(CitizenFX.Core.Matrix@,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Scales a matrix by the given value.
            </summary>
            <param name="left">The matrix to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <param name="result">When the method completes, contains the scaled matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Multiply(CitizenFX.Core.Matrix,System.Single)">
            <summary>
            Scales a matrix by the given value.
            </summary>
            <param name="left">The matrix to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <returns>The scaled matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Multiply(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Determines the product of two matrices.
            </summary>
            <param name="left">The first matrix to multiply.</param>
            <param name="right">The second matrix to multiply.</param>
            <param name="result">The product of the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Multiply(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Determines the product of two matrices.
            </summary>
            <param name="left">The first matrix to multiply.</param>
            <param name="right">The second matrix to multiply.</param>
            <returns>The product of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Divide(CitizenFX.Core.Matrix@,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Scales a matrix by the given value.
            </summary>
            <param name="left">The matrix to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <param name="result">When the method completes, contains the scaled matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Divide(CitizenFX.Core.Matrix,System.Single)">
            <summary>
            Scales a matrix by the given value.
            </summary>
            <param name="left">The matrix to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <returns>The scaled matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Divide(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Determines the quotient of two matrices.
            </summary>
            <param name="left">The first matrix to divide.</param>
            <param name="right">The second matrix to divide.</param>
            <param name="result">When the method completes, contains the quotient of the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Divide(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Determines the quotient of two matrices.
            </summary>
            <param name="left">The first matrix to divide.</param>
            <param name="right">The second matrix to divide.</param>
            <returns>The quotient of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Exponent(CitizenFX.Core.Matrix@,System.Int32,CitizenFX.Core.Matrix@)">
            <summary>
            Performs the exponential operation on a matrix.
            </summary>
            <param name="value">The matrix to perform the operation on.</param>
            <param name="exponent">The exponent to raise the matrix to.</param>
            <param name="result">When the method completes, contains the exponential matrix.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="exponent"/> is negative.</exception>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Exponent(CitizenFX.Core.Matrix,System.Int32)">
            <summary>
            Performs the exponential operation on a matrix.
            </summary>
            <param name="value">The matrix to perform the operation on.</param>
            <param name="exponent">The exponent to raise the matrix to.</param>
            <returns>The exponential matrix.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="exponent"/> is negative.</exception>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Negate(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Negates a matrix.
            </summary>
            <param name="value">The matrix to be negated.</param>
            <param name="result">When the method completes, contains the negated matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Negate(CitizenFX.Core.Matrix)">
            <summary>
            Negates a matrix.
            </summary>
            <param name="value">The matrix to be negated.</param>
            <returns>The negated matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Lerp(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Performs a linear interpolation between two matrices.
            </summary>
            <param name="start">Start matrix.</param>
            <param name="end">End matrix.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the linear interpolation of the two matrices.</param>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Lerp(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix,System.Single)">
            <summary>
            Performs a linear interpolation between two matrices.
            </summary>
            <param name="start">Start matrix.</param>
            <param name="end">End matrix.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The linear interpolation of the two matrices.</returns>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.SmoothStep(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Performs a cubic interpolation between two matrices.
            </summary>
            <param name="start">Start matrix.</param>
            <param name="end">End matrix.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the cubic interpolation of the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.SmoothStep(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix,System.Single)">
            <summary>
            Performs a cubic interpolation between two matrices.
            </summary>
            <param name="start">Start matrix.</param>
            <param name="end">End matrix.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The cubic interpolation of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Transpose(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Calculates the transpose of the specified matrix.
            </summary>
            <param name="value">The matrix whose transpose is to be calculated.</param>
            <param name="result">When the method completes, contains the transpose of the specified matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.TransposeByRef(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Calculates the transpose of the specified matrix.
            </summary>
            <param name="value">The matrix whose transpose is to be calculated.</param>
            <param name="result">When the method completes, contains the transpose of the specified matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Transpose(CitizenFX.Core.Matrix)">
            <summary>
            Calculates the transpose of the specified matrix.
            </summary>
            <param name="value">The matrix whose transpose is to be calculated.</param>
            <returns>The transpose of the specified matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Invert(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Calculates the inverse of the specified matrix.
            </summary>
            <param name="value">The matrix whose inverse is to be calculated.</param>
            <param name="result">When the method completes, contains the inverse of the specified matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Invert(CitizenFX.Core.Matrix)">
            <summary>
            Calculates the inverse of the specified matrix.
            </summary>
            <param name="value">The matrix whose inverse is to be calculated.</param>
            <returns>The inverse of the specified matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Orthogonalize(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Orthogonalizes the specified matrix.
            </summary>
            <param name="value">The matrix to orthogonalize.</param>
            <param name="result">When the method completes, contains the orthogonalized matrix.</param>
            <remarks>
            <para>Orthogonalization is the process of making all rows orthogonal to each other. This
            means that any given row in the matrix will be orthogonal to any other given row in the
            matrix.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting matrix
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the matrix rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Orthogonalize(CitizenFX.Core.Matrix)">
            <summary>
            Orthogonalizes the specified matrix.
            </summary>
            <param name="value">The matrix to orthogonalize.</param>
            <returns>The orthogonalized matrix.</returns>
            <remarks>
            <para>Orthogonalization is the process of making all rows orthogonal to each other. This
            means that any given row in the matrix will be orthogonal to any other given row in the
            matrix.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting matrix
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the matrix rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Orthonormalize(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Orthonormalizes the specified matrix.
            </summary>
            <param name="value">The matrix to orthonormalize.</param>
            <param name="result">When the method completes, contains the orthonormalized matrix.</param>
            <remarks>
            <para>Orthonormalization is the process of making all rows and columns orthogonal to each
            other and making all rows and columns of unit length. This means that any given row will
            be orthogonal to any other given row and any given column will be orthogonal to any other
            given column. Any given row will not be orthogonal to any given column. Every row and every
            column will be of unit length.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting matrix
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the matrix rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Orthonormalize(CitizenFX.Core.Matrix)">
            <summary>
            Orthonormalizes the specified matrix.
            </summary>
            <param name="value">The matrix to orthonormalize.</param>
            <returns>The orthonormalized matrix.</returns>
            <remarks>
            <para>Orthonormalization is the process of making all rows and columns orthogonal to each
            other and making all rows and columns of unit length. This means that any given row will
            be orthogonal to any other given row and any given column will be orthogonal to any other
            given column. Any given row will not be orthogonal to any given column. Every row and every
            column will be of unit length.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting matrix
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the matrix rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.UpperTriangularForm(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Brings the matrix into upper triangular form using elementary row operations.
            </summary>
            <param name="value">The matrix to put into upper triangular form.</param>
            <param name="result">When the method completes, contains the upper triangular matrix.</param>
            <remarks>
            If the matrix is not invertible (i.e. its determinant is zero) than the result of this
            method may produce Single.Nan and Single.Inf values. When the matrix represents a system
            of linear equations, than this often means that either no solution exists or an infinite
            number of solutions exist.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.UpperTriangularForm(CitizenFX.Core.Matrix)">
            <summary>
            Brings the matrix into upper triangular form using elementary row operations.
            </summary>
            <param name="value">The matrix to put into upper triangular form.</param>
            <returns>The upper triangular matrix.</returns>
            <remarks>
            If the matrix is not invertible (i.e. its determinant is zero) than the result of this
            method may produce Single.Nan and Single.Inf values. When the matrix represents a system
            of linear equations, than this often means that either no solution exists or an infinite
            number of solutions exist.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.LowerTriangularForm(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Brings the matrix into lower triangular form using elementary row operations.
            </summary>
            <param name="value">The matrix to put into lower triangular form.</param>
            <param name="result">When the method completes, contains the lower triangular matrix.</param>
            <remarks>
            If the matrix is not invertible (i.e. its determinant is zero) than the result of this
            method may produce Single.Nan and Single.Inf values. When the matrix represents a system
            of linear equations, than this often means that either no solution exists or an infinite
            number of solutions exist.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.LowerTriangularForm(CitizenFX.Core.Matrix)">
            <summary>
            Brings the matrix into lower triangular form using elementary row operations.
            </summary>
            <param name="value">The matrix to put into lower triangular form.</param>
            <returns>The lower triangular matrix.</returns>
            <remarks>
            If the matrix is not invertible (i.e. its determinant is zero) than the result of this
            method may produce Single.Nan and Single.Inf values. When the matrix represents a system
            of linear equations, than this often means that either no solution exists or an infinite
            number of solutions exist.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RowEchelonForm(CitizenFX.Core.Matrix@,CitizenFX.Core.Matrix@)">
            <summary>
            Brings the matrix into row echelon form using elementary row operations;
            </summary>
            <param name="value">The matrix to put into row echelon form.</param>
            <param name="result">When the method completes, contains the row echelon form of the matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RowEchelonForm(CitizenFX.Core.Matrix)">
            <summary>
            Brings the matrix into row echelon form using elementary row operations;
            </summary>
            <param name="value">The matrix to put into row echelon form.</param>
            <returns>When the method completes, contains the row echelon form of the matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.ReducedRowEchelonForm(CitizenFX.Core.Matrix@,CitizenFX.Core.Vector4@,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector4@)">
            <summary>
            Brings the matrix into reduced row echelon form using elementary row operations.
            </summary>
            <param name="value">The matrix to put into reduced row echelon form.</param>
            <param name="augment">The fifth column of the matrix.</param>
            <param name="result">When the method completes, contains the resultant matrix after the operation.</param>
            <param name="augmentResult">When the method completes, contains the resultant fifth column of the matrix.</param>
            <remarks>
            <para>The fifth column is often called the augmented part of the matrix. This is because the fifth
            column is really just an extension of the matrix so that there is a place to put all of the
            non-zero components after the operation is complete.</para>
            <para>Often times the resultant matrix will the identity matrix or a matrix similar to the identity
            matrix. Sometimes, however, that is not possible and numbers other than zero and one may appear.</para>
            <para>This method can be used to solve systems of linear equations. Upon completion of this method,
            the <paramref name="augmentResult"/> will contain the solution for the system. It is up to the user
            to analyze both the input and the result to determine if a solution really exists.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix.BillboardLH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a left-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <param name="result">When the method completes, contains the created billboard matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.BillboardLH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a left-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <returns>The created billboard matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.BillboardRH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a right-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <param name="result">When the method completes, contains the created billboard matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.BillboardRH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a right-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <returns>The created billboard matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.LookAtLH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a left-handed, look-at matrix.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <param name="result">When the method completes, contains the created look-at matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.LookAtLH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a left-handed, look-at matrix.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <returns>The created look-at matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.LookAtRH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a right-handed, look-at matrix.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <param name="result">When the method completes, contains the created look-at matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.LookAtRH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a right-handed, look-at matrix.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <returns>The created look-at matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.OrthoLH(System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a left-handed, orthographic projection matrix.
            </summary>
            <param name="width">Width of the viewing volume.</param>
            <param name="height">Height of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.OrthoLH(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a left-handed, orthographic projection matrix.
            </summary>
            <param name="width">Width of the viewing volume.</param>
            <param name="height">Height of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.OrthoRH(System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a right-handed, orthographic projection matrix.
            </summary>
            <param name="width">Width of the viewing volume.</param>
            <param name="height">Height of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.OrthoRH(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a right-handed, orthographic projection matrix.
            </summary>
            <param name="width">Width of the viewing volume.</param>
            <param name="height">Height of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.OrthoOffCenterLH(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a left-handed, customized orthographic projection matrix.
            </summary>
            <param name="left">Minimum x-value of the viewing volume.</param>
            <param name="right">Maximum x-value of the viewing volume.</param>
            <param name="bottom">Minimum y-value of the viewing volume.</param>
            <param name="top">Maximum y-value of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.OrthoOffCenterLH(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a left-handed, customized orthographic projection matrix.
            </summary>
            <param name="left">Minimum x-value of the viewing volume.</param>
            <param name="right">Maximum x-value of the viewing volume.</param>
            <param name="bottom">Minimum y-value of the viewing volume.</param>
            <param name="top">Maximum y-value of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.OrthoOffCenterRH(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a right-handed, customized orthographic projection matrix.
            </summary>
            <param name="left">Minimum x-value of the viewing volume.</param>
            <param name="right">Maximum x-value of the viewing volume.</param>
            <param name="bottom">Minimum y-value of the viewing volume.</param>
            <param name="top">Maximum y-value of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.OrthoOffCenterRH(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a right-handed, customized orthographic projection matrix.
            </summary>
            <param name="left">Minimum x-value of the viewing volume.</param>
            <param name="right">Maximum x-value of the viewing volume.</param>
            <param name="bottom">Minimum y-value of the viewing volume.</param>
            <param name="top">Maximum y-value of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveLH(System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a left-handed, perspective projection matrix.
            </summary>
            <param name="width">Width of the viewing volume.</param>
            <param name="height">Height of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveLH(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a left-handed, perspective projection matrix.
            </summary>
            <param name="width">Width of the viewing volume.</param>
            <param name="height">Height of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveRH(System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a right-handed, perspective projection matrix.
            </summary>
            <param name="width">Width of the viewing volume.</param>
            <param name="height">Height of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveRH(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a right-handed, perspective projection matrix.
            </summary>
            <param name="width">Width of the viewing volume.</param>
            <param name="height">Height of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveFovLH(System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a left-handed, perspective projection matrix based on a field of view.
            </summary>
            <param name="fov">Field of view in the y direction, in radians.</param>
            <param name="aspect">Aspect ratio, defined as view space width divided by height.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveFovLH(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a left-handed, perspective projection matrix based on a field of view.
            </summary>
            <param name="fov">Field of view in the y direction, in radians.</param>
            <param name="aspect">Aspect ratio, defined as view space width divided by height.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveFovRH(System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a right-handed, perspective projection matrix based on a field of view.
            </summary>
            <param name="fov">Field of view in the y direction, in radians.</param>
            <param name="aspect">Aspect ratio, defined as view space width divided by height.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveFovRH(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a right-handed, perspective projection matrix based on a field of view.
            </summary>
            <param name="fov">Field of view in the y direction, in radians.</param>
            <param name="aspect">Aspect ratio, defined as view space width divided by height.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveOffCenterLH(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a left-handed, customized perspective projection matrix.
            </summary>
            <param name="left">Minimum x-value of the viewing volume.</param>
            <param name="right">Maximum x-value of the viewing volume.</param>
            <param name="bottom">Minimum y-value of the viewing volume.</param>
            <param name="top">Maximum y-value of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveOffCenterLH(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a left-handed, customized perspective projection matrix.
            </summary>
            <param name="left">Minimum x-value of the viewing volume.</param>
            <param name="right">Maximum x-value of the viewing volume.</param>
            <param name="bottom">Minimum y-value of the viewing volume.</param>
            <param name="top">Maximum y-value of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveOffCenterRH(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a right-handed, customized perspective projection matrix.
            </summary>
            <param name="left">Minimum x-value of the viewing volume.</param>
            <param name="right">Maximum x-value of the viewing volume.</param>
            <param name="bottom">Minimum y-value of the viewing volume.</param>
            <param name="top">Maximum y-value of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <param name="result">When the method completes, contains the created projection matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.PerspectiveOffCenterRH(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a right-handed, customized perspective projection matrix.
            </summary>
            <param name="left">Minimum x-value of the viewing volume.</param>
            <param name="right">Maximum x-value of the viewing volume.</param>
            <param name="bottom">Minimum y-value of the viewing volume.</param>
            <param name="top">Maximum y-value of the viewing volume.</param>
            <param name="znear">Minimum z-value of the viewing volume.</param>
            <param name="zfar">Maximum z-value of the viewing volume.</param>
            <returns>The created projection matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Scaling(CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a matrix that scales along the x-axis, y-axis, and y-axis.
            </summary>
            <param name="scale">Scaling factor for all three axes.</param>
            <param name="result">When the method completes, contains the created scaling matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Scaling(CitizenFX.Core.Vector3)">
            <summary>
            Creates a matrix that scales along the x-axis, y-axis, and y-axis.
            </summary>
            <param name="scale">Scaling factor for all three axes.</param>
            <returns>The created scaling matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Scaling(System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a matrix that scales along the x-axis, y-axis, and y-axis.
            </summary>
            <param name="x">Scaling factor that is applied along the x-axis.</param>
            <param name="y">Scaling factor that is applied along the y-axis.</param>
            <param name="z">Scaling factor that is applied along the z-axis.</param>
            <param name="result">When the method completes, contains the created scaling matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Scaling(System.Single,System.Single,System.Single)">
            <summary>
            Creates a matrix that scales along the x-axis, y-axis, and y-axis.
            </summary>
            <param name="x">Scaling factor that is applied along the x-axis.</param>
            <param name="y">Scaling factor that is applied along the y-axis.</param>
            <param name="z">Scaling factor that is applied along the z-axis.</param>
            <returns>The created scaling matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Scaling(System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a matrix that uniformly scales along all three axis.
            </summary>
            <param name="scale">The uniform scale that is applied along all axis.</param>
            <param name="result">When the method completes, contains the created scaling matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Scaling(System.Single)">
            <summary>
            Creates a matrix that uniformly scales along all three axis.
            </summary>
            <param name="scale">The uniform scale that is applied along all axis.</param>
            <returns>The created scaling matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationX(System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a matrix that rotates around the x-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <param name="result">When the method completes, contains the created rotation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationX(System.Single)">
            <summary>
            Creates a matrix that rotates around the x-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <returns>The created rotation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationY(System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a matrix that rotates around the y-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <param name="result">When the method completes, contains the created rotation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationY(System.Single)">
            <summary>
            Creates a matrix that rotates around the y-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <returns>The created rotation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationZ(System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a matrix that rotates around the z-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <param name="result">When the method completes, contains the created rotation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationZ(System.Single)">
            <summary>
            Creates a matrix that rotates around the z-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <returns>The created rotation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationAxis(CitizenFX.Core.Vector3@,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a matrix that rotates around an arbitrary axis.
            </summary>
            <param name="axis">The axis around which to rotate. This parameter is assumed to be normalized.</param>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <param name="result">When the method completes, contains the created rotation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationAxis(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Creates a matrix that rotates around an arbitrary axis.
            </summary>
            <param name="axis">The axis around which to rotate. This parameter is assumed to be normalized.</param>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <returns>The created rotation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationQuaternion(CitizenFX.Core.Quaternion@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a rotation matrix from a quaternion.
            </summary>
            <param name="rotation">The quaternion to use to build the matrix.</param>
            <param name="result">The created rotation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationQuaternion(CitizenFX.Core.Quaternion)">
            <summary>
            Creates a rotation matrix from a quaternion.
            </summary>
            <param name="rotation">The quaternion to use to build the matrix.</param>
            <returns>The created rotation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationYawPitchRoll(System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a rotation matrix with a specified yaw, pitch, and roll.
            </summary>
            <param name="yaw">Yaw around the y-axis, in radians.</param>
            <param name="pitch">Pitch around the x-axis, in radians.</param>
            <param name="roll">Roll around the z-axis, in radians.</param>
            <param name="result">When the method completes, contains the created rotation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.RotationYawPitchRoll(System.Single,System.Single,System.Single)">
            <summary>
            Creates a rotation matrix with a specified yaw, pitch, and roll.
            </summary>
            <param name="yaw">Yaw around the y-axis, in radians.</param>
            <param name="pitch">Pitch around the x-axis, in radians.</param>
            <param name="roll">Roll around the z-axis, in radians.</param>
            <returns>The created rotation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Translation(CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a translation matrix using the specified offsets.
            </summary>
            <param name="value">The offset for all three coordinate planes.</param>
            <param name="result">When the method completes, contains the created translation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Translation(CitizenFX.Core.Vector3)">
            <summary>
            Creates a translation matrix using the specified offsets.
            </summary>
            <param name="value">The offset for all three coordinate planes.</param>
            <returns>The created translation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Translation(System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a translation matrix using the specified offsets.
            </summary>
            <param name="x">X-coordinate offset.</param>
            <param name="y">Y-coordinate offset.</param>
            <param name="z">Z-coordinate offset.</param>
            <param name="result">When the method completes, contains the created translation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Translation(System.Single,System.Single,System.Single)">
            <summary>
            Creates a translation matrix using the specified offsets.
            </summary>
            <param name="x">X-coordinate offset.</param>
            <param name="y">Y-coordinate offset.</param>
            <param name="z">Z-coordinate offset.</param>
            <returns>The created translation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Skew(System.Single,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a skew/shear matrix by means of a translation vector, a rotation vector, and a rotation angle.
            shearing is performed in the direction of translation vector, where translation vector and rotation vector define the shearing plane.
            The effect is such that the skewed rotation vector has the specified angle with rotation itself.
            </summary>
            <param name="angle">The rotation angle.</param>
            <param name="rotationVec">The rotation vector</param>
            <param name="transVec">The translation vector</param>
            <param name="matrix">Contains the created skew/shear matrix. </param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.AffineTransformation(System.Single,CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a 3D affine transformation matrix.
            </summary>
            <param name="scaling">Scaling factor.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <param name="result">When the method completes, contains the created affine transformation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.AffineTransformation(System.Single,CitizenFX.Core.Quaternion,CitizenFX.Core.Vector3)">
            <summary>
            Creates a 3D affine transformation matrix.
            </summary>
            <param name="scaling">Scaling factor.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <returns>The created affine transformation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.AffineTransformation(System.Single,CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a 3D affine transformation matrix.
            </summary>
            <param name="scaling">Scaling factor.</param>
            <param name="rotationCenter">The center of the rotation.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <param name="result">When the method completes, contains the created affine transformation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.AffineTransformation(System.Single,CitizenFX.Core.Vector3,CitizenFX.Core.Quaternion,CitizenFX.Core.Vector3)">
            <summary>
            Creates a 3D affine transformation matrix.
            </summary>
            <param name="scaling">Scaling factor.</param>
            <param name="rotationCenter">The center of the rotation.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <returns>The created affine transformation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.AffineTransformation2D(System.Single,System.Single,CitizenFX.Core.Vector2@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a 2D affine transformation matrix.
            </summary>
            <param name="scaling">Scaling factor.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <param name="result">When the method completes, contains the created affine transformation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.AffineTransformation2D(System.Single,System.Single,CitizenFX.Core.Vector2)">
            <summary>
            Creates a 2D affine transformation matrix.
            </summary>
            <param name="scaling">Scaling factor.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <returns>The created affine transformation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.AffineTransformation2D(System.Single,CitizenFX.Core.Vector2@,System.Single,CitizenFX.Core.Vector2@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a 2D affine transformation matrix.
            </summary>
            <param name="scaling">Scaling factor.</param>
            <param name="rotationCenter">The center of the rotation.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <param name="result">When the method completes, contains the created affine transformation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.AffineTransformation2D(System.Single,CitizenFX.Core.Vector2,System.Single,CitizenFX.Core.Vector2)">
            <summary>
            Creates a 2D affine transformation matrix.
            </summary>
            <param name="scaling">Scaling factor.</param>
            <param name="rotationCenter">The center of the rotation.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <returns>The created affine transformation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Transformation(CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a transformation matrix.
            </summary>
            <param name="scalingCenter">Center point of the scaling operation.</param>
            <param name="scalingRotation">Scaling rotation amount.</param>
            <param name="scaling">Scaling factor.</param>
            <param name="rotationCenter">The center of the rotation.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <param name="result">When the method completes, contains the created transformation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Transformation(CitizenFX.Core.Vector3,CitizenFX.Core.Quaternion,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Quaternion,CitizenFX.Core.Vector3)">
            <summary>
            Creates a transformation matrix.
            </summary>
            <param name="scalingCenter">Center point of the scaling operation.</param>
            <param name="scalingRotation">Scaling rotation amount.</param>
            <param name="scaling">Scaling factor.</param>
            <param name="rotationCenter">The center of the rotation.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <returns>The created transformation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Transformation2D(CitizenFX.Core.Vector2@,System.Single,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single,CitizenFX.Core.Vector2@,CitizenFX.Core.Matrix@)">
            <summary>
            Creates a 2D transformation matrix.
            </summary>
            <param name="scalingCenter">Center point of the scaling operation.</param>
            <param name="scalingRotation">Scaling rotation amount.</param>
            <param name="scaling">Scaling factor.</param>
            <param name="rotationCenter">The center of the rotation.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <param name="result">When the method completes, contains the created transformation matrix.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Transformation2D(CitizenFX.Core.Vector2,System.Single,CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,System.Single,CitizenFX.Core.Vector2)">
            <summary>
            Creates a 2D transformation matrix.
            </summary>
            <param name="scalingCenter">Center point of the scaling operation.</param>
            <param name="scalingRotation">Scaling rotation amount.</param>
            <param name="scaling">Scaling factor.</param>
            <param name="rotationCenter">The center of the rotation.</param>
            <param name="rotation">The rotation of the transformation.</param>
            <param name="translation">The translation factor of the transformation.</param>
            <returns>The created transformation matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_Addition(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Adds two matrices.
            </summary>
            <param name="left">The first matrix to add.</param>
            <param name="right">The second matrix to add.</param>
            <returns>The sum of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_UnaryPlus(CitizenFX.Core.Matrix)">
            <summary>
            Assert a matrix (return it unchanged).
            </summary>
            <param name="value">The matrix to assert (unchanged).</param>
            <returns>The asserted (unchanged) matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_Subtraction(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Subtracts two matrices.
            </summary>
            <param name="left">The first matrix to subtract.</param>
            <param name="right">The second matrix to subtract.</param>
            <returns>The difference between the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_UnaryNegation(CitizenFX.Core.Matrix)">
            <summary>
            Negates a matrix.
            </summary>
            <param name="value">The matrix to negate.</param>
            <returns>The negated matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_Multiply(System.Single,CitizenFX.Core.Matrix)">
            <summary>
            Scales a matrix by a given value.
            </summary>
            <param name="right">The matrix to scale.</param>
            <param name="left">The amount by which to scale.</param>
            <returns>The scaled matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_Multiply(CitizenFX.Core.Matrix,System.Single)">
            <summary>
            Scales a matrix by a given value.
            </summary>
            <param name="left">The matrix to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <returns>The scaled matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_Multiply(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Multiplies two matrices.
            </summary>
            <param name="left">The first matrix to multiply.</param>
            <param name="right">The second matrix to multiply.</param>
            <returns>The product of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_Division(CitizenFX.Core.Matrix,System.Single)">
            <summary>
            Scales a matrix by a given value.
            </summary>
            <param name="left">The matrix to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <returns>The scaled matrix.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_Division(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Divides two matrices.
            </summary>
            <param name="left">The first matrix to divide.</param>
            <param name="right">The second matrix to divide.</param>
            <returns>The quotient of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_Equality(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Tests for equality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has the same value as <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.op_Inequality(CitizenFX.Core.Matrix,CitizenFX.Core.Matrix)">
            <summary>
            Tests for inequality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has a different value than <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.ToString(System.String)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.ToString(System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Equals(CitizenFX.Core.Matrix@)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Matrix"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Matrix"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:CitizenFX.Core.Matrix"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Equals(CitizenFX.Core.Matrix)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Matrix"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Matrix"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:CitizenFX.Core.Matrix"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:CitizenFX.Core.Matrix3x3">
            <summary>
            Represents a 3x3 Matrix ( contains only Scale and Rotation ).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.Zero">
            <summary>
            A <see cref="T:CitizenFX.Core.Matrix3x3"/> with all of its components set to zero.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.Identity">
            <summary>
            The identity <see cref="T:CitizenFX.Core.Matrix3x3"/>.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.M11">
            <summary>
            Value at row 1 column 1 of the Matrix3x3.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.M12">
            <summary>
            Value at row 1 column 2 of the Matrix3x3.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.M13">
            <summary>
            Value at row 1 column 3 of the Matrix3x3.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.M21">
            <summary>
            Value at row 2 column 1 of the Matrix3x3.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.M22">
            <summary>
            Value at row 2 column 2 of the Matrix3x3.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.M23">
            <summary>
            Value at row 2 column 3 of the Matrix3x3.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.M31">
            <summary>
            Value at row 3 column 1 of the Matrix3x3.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.M32">
            <summary>
            Value at row 3 column 2 of the Matrix3x3.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Matrix3x3.M33">
            <summary>
            Value at row 3 column 3 of the Matrix3x3.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Matrix3x3"/> struct.
            </summary>
            <param name="value">The value that will be assigned to all components.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Matrix3x3"/> struct.
            </summary>
            <param name="M11">The value to assign at row 1 column 1 of the Matrix3x3.</param>
            <param name="M12">The value to assign at row 1 column 2 of the Matrix3x3.</param>
            <param name="M13">The value to assign at row 1 column 3 of the Matrix3x3.</param>
            <param name="M21">The value to assign at row 2 column 1 of the Matrix3x3.</param>
            <param name="M22">The value to assign at row 2 column 2 of the Matrix3x3.</param>
            <param name="M23">The value to assign at row 2 column 3 of the Matrix3x3.</param>
            <param name="M31">The value to assign at row 3 column 1 of the Matrix3x3.</param>
            <param name="M32">The value to assign at row 3 column 2 of the Matrix3x3.</param>
            <param name="M33">The value to assign at row 3 column 3 of the Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.#ctor(System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Matrix3x3"/> struct.
            </summary>
            <param name="values">The values to assign to the components of the Matrix3x3. This must be an array with sixteen elements.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="values"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="values"/> contains more or less than sixteen elements.</exception>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.Row1">
            <summary>
            Gets or sets the first row in the Matrix3x3; that is M11, M12, M13
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.Row2">
            <summary>
            Gets or sets the second row in the Matrix3x3; that is M21, M22, M23
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.Row3">
            <summary>
            Gets or sets the third row in the Matrix3x3; that is M31, M32, M33
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.Column1">
            <summary>
            Gets or sets the first column in the Matrix3x3; that is M11, M21, M31
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.Column2">
            <summary>
            Gets or sets the second column in the Matrix3x3; that is M12, M22, M32
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.Column3">
            <summary>
            Gets or sets the third column in the Matrix3x3; that is M13, M23, M33
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.ScaleVector">
            <summary>
            Gets or sets the scale of the Matrix3x3; that is M11, M22, and M33.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.IsIdentity">
            <summary>
            Gets a value indicating whether this instance is an identity Matrix3x3.
            </summary>
            <value>
            <c>true</c> if this instance is an identity Matrix3x3; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.Item(System.Int32)">
            <summary>
            Gets or sets the component at the specified index.
            </summary>
            <value>The value of the Matrix3x3 component, depending on the index.</value>
            <param name="index">The zero-based index of the component to access.</param>
            <returns>The value of the component at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="index"/> is out of the range [0, 15].</exception>
        </member>
        <member name="P:CitizenFX.Core.Matrix3x3.Item(System.Int32,System.Int32)">
            <summary>
            Gets or sets the component at the specified index.
            </summary>
            <value>The value of the Matrix3x3 component, depending on the index.</value>
            <param name="row">The row of the Matrix3x3 to access.</param>
            <param name="column">The column of the Matrix3x3 to access.</param>
            <returns>The value of the component at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="row"/> or <paramref name="column"/>is out of the range [0, 3].</exception>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Determinant">
            <summary>
            Calculates the determinant of the Matrix3x3.
            </summary>
            <returns>The determinant of the Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Invert">
            <summary>
            Inverts the Matrix3x3.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Transpose">
            <summary>
            Transposes the Matrix3x3.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Orthogonalize">
            <summary>
            Orthogonalizes the specified Matrix3x3.
            </summary>
            <remarks>
            <para>Orthogonalization is the process of making all rows orthogonal to each other. This
            means that any given row in the Matrix3x3 will be orthogonal to any other given row in the
            Matrix3x3.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting Matrix3x3
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the Matrix3x3 rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Orthonormalize">
            <summary>
            Orthonormalizes the specified Matrix3x3.
            </summary>
            <remarks>
            <para>Orthonormalization is the process of making all rows and columns orthogonal to each
            other and making all rows and columns of unit length. This means that any given row will
            be orthogonal to any other given row and any given column will be orthogonal to any other
            given column. Any given row will not be orthogonal to any given column. Every row and every
            column will be of unit length.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting Matrix3x3
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the Matrix3x3 rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.DecomposeQR(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Decomposes a Matrix3x3 into an orthonormalized Matrix3x3 Q and a right triangular Matrix3x3 R.
            </summary>
            <param name="Q">When the method completes, contains the orthonormalized Matrix3x3 of the decomposition.</param>
            <param name="R">When the method completes, contains the right triangular Matrix3x3 of the decomposition.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.DecomposeLQ(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Decomposes a Matrix3x3 into a lower triangular Matrix3x3 L and an orthonormalized Matrix3x3 Q.
            </summary>
            <param name="L">When the method completes, contains the lower triangular Matrix3x3 of the decomposition.</param>
            <param name="Q">When the method completes, contains the orthonormalized Matrix3x3 of the decomposition.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Decompose(CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@)">
            <summary>
            Decomposes a Matrix3x3 into a scale, rotation, and translation.
            </summary>
            <param name="scale">When the method completes, contains the scaling component of the decomposed Matrix3x3.</param>
            <param name="rotation">When the method completes, contains the rotation component of the decomposed Matrix3x3.</param>
            <remarks>
            This method is designed to decompose an SRT transformation Matrix3x3 only.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.DecomposeUniformScale(System.Single@,CitizenFX.Core.Quaternion@)">
            <summary>
            Decomposes a uniform scale matrix into a scale, rotation, and translation.
            A uniform scale matrix has the same scale in every axis.
            </summary>
            <param name="scale">When the method completes, contains the scaling component of the decomposed matrix.</param>
            <param name="rotation">When the method completes, contains the rotation component of the decomposed matrix.</param>
            <remarks>
            This method is designed to decompose only an SRT transformation matrix that has the same scale in every axis.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.ExchangeRows(System.Int32,System.Int32)">
            <summary>
            Exchanges two rows in the Matrix3x3.
            </summary>
            <param name="firstRow">The first row to exchange. This is an index of the row starting at zero.</param>
            <param name="secondRow">The second row to exchange. This is an index of the row starting at zero.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.ExchangeColumns(System.Int32,System.Int32)">
            <summary>
            Exchanges two columns in the Matrix3x3.
            </summary>
            <param name="firstColumn">The first column to exchange. This is an index of the column starting at zero.</param>
            <param name="secondColumn">The second column to exchange. This is an index of the column starting at zero.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.ToArray">
            <summary>
            Creates an array containing the elements of the Matrix3x3.
            </summary>
            <returns>A 9-element array containing the components of the Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Add(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Determines the sum of two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to add.</param>
            <param name="right">The second Matrix3x3 to add.</param>
            <param name="result">When the method completes, contains the sum of the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Add(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Determines the sum of two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to add.</param>
            <param name="right">The second Matrix3x3 to add.</param>
            <returns>The sum of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Subtract(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Determines the difference between two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to subtract.</param>
            <param name="right">The second Matrix3x3 to subtract.</param>
            <param name="result">When the method completes, contains the difference between the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Subtract(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Determines the difference between two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to subtract.</param>
            <param name="right">The second Matrix3x3 to subtract.</param>
            <returns>The difference between the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Multiply(CitizenFX.Core.Matrix3x3@,System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Scales a Matrix3x3 by the given value.
            </summary>
            <param name="left">The Matrix3x3 to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <param name="result">When the method completes, contains the scaled Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Multiply(CitizenFX.Core.Matrix3x3,System.Single)">
            <summary>
            Scales a Matrix3x3 by the given value.
            </summary>
            <param name="left">The Matrix3x3 to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <returns>The scaled Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Multiply(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Determines the product of two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to multiply.</param>
            <param name="right">The second Matrix3x3 to multiply.</param>
            <param name="result">The product of the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Multiply(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Determines the product of two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to multiply.</param>
            <param name="right">The second Matrix3x3 to multiply.</param>
            <returns>The product of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Divide(CitizenFX.Core.Matrix3x3@,System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Scales a Matrix3x3 by the given value.
            </summary>
            <param name="left">The Matrix3x3 to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <param name="result">When the method completes, contains the scaled Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Divide(CitizenFX.Core.Matrix3x3,System.Single)">
            <summary>
            Scales a Matrix3x3 by the given value.
            </summary>
            <param name="left">The Matrix3x3 to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <returns>The scaled Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Divide(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Determines the quotient of two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to divide.</param>
            <param name="right">The second Matrix3x3 to divide.</param>
            <param name="result">When the method completes, contains the quotient of the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Divide(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Determines the quotient of two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to divide.</param>
            <param name="right">The second Matrix3x3 to divide.</param>
            <returns>The quotient of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Exponent(CitizenFX.Core.Matrix3x3@,System.Int32,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Performs the exponential operation on a Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 to perform the operation on.</param>
            <param name="exponent">The exponent to raise the Matrix3x3 to.</param>
            <param name="result">When the method completes, contains the exponential Matrix3x3.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="exponent"/> is negative.</exception>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Exponent(CitizenFX.Core.Matrix3x3,System.Int32)">
            <summary>
            Performs the exponential operation on a Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 to perform the operation on.</param>
            <param name="exponent">The exponent to raise the Matrix3x3 to.</param>
            <returns>The exponential Matrix3x3.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="exponent"/> is negative.</exception>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Negate(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Negates a Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 to be negated.</param>
            <param name="result">When the method completes, contains the negated Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Negate(CitizenFX.Core.Matrix3x3)">
            <summary>
            Negates a Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 to be negated.</param>
            <returns>The negated Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Lerp(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@,System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Performs a linear interpolation between two matrices.
            </summary>
            <param name="start">Start Matrix3x3.</param>
            <param name="end">End Matrix3x3.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the linear interpolation of the two matrices.</param>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Lerp(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3,System.Single)">
            <summary>
            Performs a linear interpolation between two matrices.
            </summary>
            <param name="start">Start Matrix3x3.</param>
            <param name="end">End Matrix3x3.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The linear interpolation of the two matrices.</returns>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.SmoothStep(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@,System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Performs a cubic interpolation between two matrices.
            </summary>
            <param name="start">Start Matrix3x3.</param>
            <param name="end">End Matrix3x3.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the cubic interpolation of the two matrices.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.SmoothStep(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3,System.Single)">
            <summary>
            Performs a cubic interpolation between two matrices.
            </summary>
            <param name="start">Start Matrix3x3.</param>
            <param name="end">End Matrix3x3.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The cubic interpolation of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Transpose(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Calculates the transpose of the specified Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 whose transpose is to be calculated.</param>
            <param name="result">When the method completes, contains the transpose of the specified Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.TransposeByRef(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Calculates the transpose of the specified Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 whose transpose is to be calculated.</param>
            <param name="result">When the method completes, contains the transpose of the specified Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Transpose(CitizenFX.Core.Matrix3x3)">
            <summary>
            Calculates the transpose of the specified Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 whose transpose is to be calculated.</param>
            <returns>The transpose of the specified Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Invert(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Calculates the inverse of the specified Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 whose inverse is to be calculated.</param>
            <param name="result">When the method completes, contains the inverse of the specified Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Invert(CitizenFX.Core.Matrix3x3)">
            <summary>
            Calculates the inverse of the specified Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 whose inverse is to be calculated.</param>
            <returns>The inverse of the specified Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Orthogonalize(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Orthogonalizes the specified Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 to orthogonalize.</param>
            <param name="result">When the method completes, contains the orthogonalized Matrix3x3.</param>
            <remarks>
            <para>Orthogonalization is the process of making all rows orthogonal to each other. This
            means that any given row in the Matrix3x3 will be orthogonal to any other given row in the
            Matrix3x3.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting Matrix3x3
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the Matrix3x3 rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Orthogonalize(CitizenFX.Core.Matrix3x3)">
            <summary>
            Orthogonalizes the specified Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 to orthogonalize.</param>
            <returns>The orthogonalized Matrix3x3.</returns>
            <remarks>
            <para>Orthogonalization is the process of making all rows orthogonal to each other. This
            means that any given row in the Matrix3x3 will be orthogonal to any other given row in the
            Matrix3x3.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting Matrix3x3
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the Matrix3x3 rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Orthonormalize(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Orthonormalizes the specified Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 to orthonormalize.</param>
            <param name="result">When the method completes, contains the orthonormalized Matrix3x3.</param>
            <remarks>
            <para>Orthonormalization is the process of making all rows and columns orthogonal to each
            other and making all rows and columns of unit length. This means that any given row will
            be orthogonal to any other given row and any given column will be orthogonal to any other
            given column. Any given row will not be orthogonal to any given column. Every row and every
            column will be of unit length.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting Matrix3x3
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the Matrix3x3 rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Orthonormalize(CitizenFX.Core.Matrix3x3)">
            <summary>
            Orthonormalizes the specified Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 to orthonormalize.</param>
            <returns>The orthonormalized Matrix3x3.</returns>
            <remarks>
            <para>Orthonormalization is the process of making all rows and columns orthogonal to each
            other and making all rows and columns of unit length. This means that any given row will
            be orthogonal to any other given row and any given column will be orthogonal to any other
            given column. Any given row will not be orthogonal to any given column. Every row and every
            column will be of unit length.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting Matrix3x3
            tends to be numerically unstable. The numeric stability decreases according to the rows
            so that the first row is the most stable and the last row is the least stable.</para>
            <para>This operation is performed on the rows of the Matrix3x3 rather than the columns.
            If you wish for this operation to be performed on the columns, first transpose the
            input and than transpose the output.</para>
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.UpperTriangularForm(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Brings the Matrix3x3 into upper triangular form using elementary row operations.
            </summary>
            <param name="value">The Matrix3x3 to put into upper triangular form.</param>
            <param name="result">When the method completes, contains the upper triangular Matrix3x3.</param>
            <remarks>
            If the Matrix3x3 is not invertible (i.e. its determinant is zero) than the result of this
            method may produce Single.Nan and Single.Inf values. When the Matrix3x3 represents a system
            of linear equations, than this often means that either no solution exists or an infinite
            number of solutions exist.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.UpperTriangularForm(CitizenFX.Core.Matrix3x3)">
            <summary>
            Brings the Matrix3x3 into upper triangular form using elementary row operations.
            </summary>
            <param name="value">The Matrix3x3 to put into upper triangular form.</param>
            <returns>The upper triangular Matrix3x3.</returns>
            <remarks>
            If the Matrix3x3 is not invertible (i.e. its determinant is zero) than the result of this
            method may produce Single.Nan and Single.Inf values. When the Matrix3x3 represents a system
            of linear equations, than this often means that either no solution exists or an infinite
            number of solutions exist.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.LowerTriangularForm(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Brings the Matrix3x3 into lower triangular form using elementary row operations.
            </summary>
            <param name="value">The Matrix3x3 to put into lower triangular form.</param>
            <param name="result">When the method completes, contains the lower triangular Matrix3x3.</param>
            <remarks>
            If the Matrix3x3 is not invertible (i.e. its determinant is zero) than the result of this
            method may produce Single.Nan and Single.Inf values. When the Matrix3x3 represents a system
            of linear equations, than this often means that either no solution exists or an infinite
            number of solutions exist.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.LowerTriangularForm(CitizenFX.Core.Matrix3x3)">
            <summary>
            Brings the Matrix3x3 into lower triangular form using elementary row operations.
            </summary>
            <param name="value">The Matrix3x3 to put into lower triangular form.</param>
            <returns>The lower triangular Matrix3x3.</returns>
            <remarks>
            If the Matrix3x3 is not invertible (i.e. its determinant is zero) than the result of this
            method may produce Single.Nan and Single.Inf values. When the Matrix3x3 represents a system
            of linear equations, than this often means that either no solution exists or an infinite
            number of solutions exist.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RowEchelonForm(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Brings the Matrix3x3 into row echelon form using elementary row operations;
            </summary>
            <param name="value">The Matrix3x3 to put into row echelon form.</param>
            <param name="result">When the method completes, contains the row echelon form of the Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RowEchelonForm(CitizenFX.Core.Matrix3x3)">
            <summary>
            Brings the Matrix3x3 into row echelon form using elementary row operations;
            </summary>
            <param name="value">The Matrix3x3 to put into row echelon form.</param>
            <returns>When the method completes, contains the row echelon form of the Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.BillboardLH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a left-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <param name="result">When the method completes, contains the created billboard Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.BillboardLH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a left-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <returns>The created billboard Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.BillboardRH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a right-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <param name="result">When the method completes, contains the created billboard Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.BillboardRH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a right-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <returns>The created billboard Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.LookAtLH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a left-handed, look-at Matrix3x3.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <param name="result">When the method completes, contains the created look-at Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.LookAtLH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a left-handed, look-at Matrix3x3.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <returns>The created look-at Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.LookAtRH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a right-handed, look-at Matrix3x3.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <param name="result">When the method completes, contains the created look-at Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.LookAtRH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a right-handed, look-at Matrix3x3.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <returns>The created look-at Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Scaling(CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a Matrix3x3 that scales along the x-axis, y-axis, and y-axis.
            </summary>
            <param name="scale">Scaling factor for all three axes.</param>
            <param name="result">When the method completes, contains the created scaling Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Scaling(CitizenFX.Core.Vector3)">
            <summary>
            Creates a Matrix3x3 that scales along the x-axis, y-axis, and y-axis.
            </summary>
            <param name="scale">Scaling factor for all three axes.</param>
            <returns>The created scaling Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Scaling(System.Single,System.Single,System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a Matrix3x3 that scales along the x-axis, y-axis, and y-axis.
            </summary>
            <param name="x">Scaling factor that is applied along the x-axis.</param>
            <param name="y">Scaling factor that is applied along the y-axis.</param>
            <param name="z">Scaling factor that is applied along the z-axis.</param>
            <param name="result">When the method completes, contains the created scaling Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Scaling(System.Single,System.Single,System.Single)">
            <summary>
            Creates a Matrix3x3 that scales along the x-axis, y-axis, and y-axis.
            </summary>
            <param name="x">Scaling factor that is applied along the x-axis.</param>
            <param name="y">Scaling factor that is applied along the y-axis.</param>
            <param name="z">Scaling factor that is applied along the z-axis.</param>
            <returns>The created scaling Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Scaling(System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a Matrix3x3 that uniformly scales along all three axis.
            </summary>
            <param name="scale">The uniform scale that is applied along all axis.</param>
            <param name="result">When the method completes, contains the created scaling Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Scaling(System.Single)">
            <summary>
            Creates a Matrix3x3 that uniformly scales along all three axis.
            </summary>
            <param name="scale">The uniform scale that is applied along all axis.</param>
            <returns>The created scaling Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationX(System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a Matrix3x3 that rotates around the x-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <param name="result">When the method completes, contains the created rotation Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationX(System.Single)">
            <summary>
            Creates a Matrix3x3 that rotates around the x-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <returns>The created rotation Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationY(System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a Matrix3x3 that rotates around the y-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <param name="result">When the method completes, contains the created rotation Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationY(System.Single)">
            <summary>
            Creates a Matrix3x3 that rotates around the y-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <returns>The created rotation Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationZ(System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a Matrix3x3 that rotates around the z-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <param name="result">When the method completes, contains the created rotation Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationZ(System.Single)">
            <summary>
            Creates a Matrix3x3 that rotates around the z-axis.
            </summary>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <returns>The created rotation Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationAxis(CitizenFX.Core.Vector3@,System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a Matrix3x3 that rotates around an arbitrary axis.
            </summary>
            <param name="axis">The axis around which to rotate. This parameter is assumed to be normalized.</param>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <param name="result">When the method completes, contains the created rotation Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationAxis(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Creates a Matrix3x3 that rotates around an arbitrary axis.
            </summary>
            <param name="axis">The axis around which to rotate. This parameter is assumed to be normalized.</param>
            <param name="angle">Angle of rotation in radians. Angles are measured clockwise when looking along the rotation axis toward the origin.</param>
            <returns>The created rotation Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationQuaternion(CitizenFX.Core.Quaternion@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a rotation Matrix3x3 from a quaternion.
            </summary>
            <param name="rotation">The quaternion to use to build the Matrix3x3.</param>
            <param name="result">The created rotation Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationQuaternion(CitizenFX.Core.Quaternion)">
            <summary>
            Creates a rotation Matrix3x3 from a quaternion.
            </summary>
            <param name="rotation">The quaternion to use to build the Matrix3x3.</param>
            <returns>The created rotation Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationYawPitchRoll(System.Single,System.Single,System.Single,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Creates a rotation Matrix3x3 with a specified yaw, pitch, and roll.
            </summary>
            <param name="yaw">Yaw around the y-axis, in radians.</param>
            <param name="pitch">Pitch around the x-axis, in radians.</param>
            <param name="roll">Roll around the z-axis, in radians.</param>
            <param name="result">When the method completes, contains the created rotation Matrix3x3.</param>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.RotationYawPitchRoll(System.Single,System.Single,System.Single)">
            <summary>
            Creates a rotation Matrix3x3 with a specified yaw, pitch, and roll.
            </summary>
            <param name="yaw">Yaw around the y-axis, in radians.</param>
            <param name="pitch">Pitch around the x-axis, in radians.</param>
            <param name="roll">Roll around the z-axis, in radians.</param>
            <returns>The created rotation Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Addition(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Adds two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to add.</param>
            <param name="right">The second Matrix3x3 to add.</param>
            <returns>The sum of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_UnaryPlus(CitizenFX.Core.Matrix3x3)">
            <summary>
            Assert a Matrix3x3 (return it unchanged).
            </summary>
            <param name="value">The Matrix3x3 to assert (unchanged).</param>
            <returns>The asserted (unchanged) Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Subtraction(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Subtracts two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to subtract.</param>
            <param name="right">The second Matrix3x3 to subtract.</param>
            <returns>The difference between the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_UnaryNegation(CitizenFX.Core.Matrix3x3)">
            <summary>
            Negates a Matrix3x3.
            </summary>
            <param name="value">The Matrix3x3 to negate.</param>
            <returns>The negated Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Multiply(System.Single,CitizenFX.Core.Matrix3x3)">
            <summary>
            Scales a Matrix3x3 by a given value.
            </summary>
            <param name="right">The Matrix3x3 to scale.</param>
            <param name="left">The amount by which to scale.</param>
            <returns>The scaled Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Multiply(CitizenFX.Core.Matrix3x3,System.Single)">
            <summary>
            Scales a Matrix3x3 by a given value.
            </summary>
            <param name="left">The Matrix3x3 to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <returns>The scaled Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Multiply(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Multiplies two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to multiply.</param>
            <param name="right">The second Matrix3x3 to multiply.</param>
            <returns>The product of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Division(CitizenFX.Core.Matrix3x3,System.Single)">
            <summary>
            Scales a Matrix3x3 by a given value.
            </summary>
            <param name="left">The Matrix3x3 to scale.</param>
            <param name="right">The amount by which to scale.</param>
            <returns>The scaled Matrix3x3.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Division(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Divides two matrices.
            </summary>
            <param name="left">The first Matrix3x3 to divide.</param>
            <param name="right">The second Matrix3x3 to divide.</param>
            <returns>The quotient of the two matrices.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Equality(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Tests for equality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has the same value as <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Inequality(CitizenFX.Core.Matrix3x3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Tests for inequality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has a different value than <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Explicit(CitizenFX.Core.Matrix3x3)~CitizenFX.Core.Matrix">
            <summary>
            Convert the 3x3 Matrix to a 4x4 Matrix.
            </summary>
            <returns>A 4x4 Matrix with zero translation and M44=1</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.op_Explicit(CitizenFX.Core.Matrix)~CitizenFX.Core.Matrix3x3">
            <summary>
            Convert the 4x4 Matrix to a 3x3 Matrix.
            </summary>
            <returns>A 3x3 Matrix</returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.ToString(System.String)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.ToString(System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Equals(CitizenFX.Core.Matrix3x3@)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Matrix3x3"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Matrix3x3"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:CitizenFX.Core.Matrix3x3"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Equals(CitizenFX.Core.Matrix3x3)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Matrix3x3"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Matrix3x3"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:CitizenFX.Core.Matrix3x3"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Equals(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Matrix3x3@)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Matrix3x3"/> are equal.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Matrix3x3.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:CitizenFX.Core.Quaternion">
            <summary>
            Represents a four dimensional mathematical quaternion.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Quaternion.Zero">
            <summary>
            A <see cref="T:CitizenFX.Core.Quaternion"/> with all of its components set to zero.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Quaternion.One">
            <summary>
            A <see cref="T:CitizenFX.Core.Quaternion"/> with all of its components set to one.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Quaternion.Identity">
            <summary>
            The identity <see cref="T:CitizenFX.Core.Quaternion"/> (0, 0, 0, 1).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Quaternion.X">
            <summary>
            The X component of the quaternion.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Quaternion.Y">
            <summary>
            The Y component of the quaternion.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Quaternion.Z">
            <summary>
            The Z component of the quaternion.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Quaternion.W">
            <summary>
            The W component of the quaternion.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Quaternion"/> struct.
            </summary>
            <param name="value">The value that will be assigned to all components.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.#ctor(CitizenFX.Core.Vector4)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Quaternion"/> struct.
            </summary>
            <param name="value">A vector containing the values with which to initialize the components.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.#ctor(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Quaternion"/> struct.
            </summary>
            <param name="value">A vector containing the values with which to initialize the X, Y, and Z components.</param>
            <param name="w">Initial value for the W component of the quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.#ctor(CitizenFX.Core.Vector2,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Quaternion"/> struct.
            </summary>
            <param name="value">A vector containing the values with which to initialize the X and Y components.</param>
            <param name="z">Initial value for the Z component of the quaternion.</param>
            <param name="w">Initial value for the W component of the quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Quaternion"/> struct.
            </summary>
            <param name="x">Initial value for the X component of the quaternion.</param>
            <param name="y">Initial value for the Y component of the quaternion.</param>
            <param name="z">Initial value for the Z component of the quaternion.</param>
            <param name="w">Initial value for the W component of the quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.#ctor(System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Quaternion"/> struct.
            </summary>
            <param name="values">The values to assign to the X, Y, Z, and W components of the quaternion. This must be an array with four elements.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="values"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="values"/> contains more or less than four elements.</exception>
        </member>
        <member name="P:CitizenFX.Core.Quaternion.IsIdentity">
            <summary>
            Gets a value indicating whether this instance is equivalent to the identity quaternion.
            </summary>
            <value>
            <c>true</c> if this instance is an identity quaternion; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:CitizenFX.Core.Quaternion.IsNormalized">
            <summary>
            Gets a value indicting whether this instance is normalized.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Quaternion.Angle">
            <summary>
            Gets the angle of the quaternion.
            </summary>
            <value>The quaternion's angle.</value>
        </member>
        <member name="P:CitizenFX.Core.Quaternion.Axis">
            <summary>
            Gets the axis components of the quaternion.
            </summary>
            <value>The axis components of the quaternion.</value>
        </member>
        <member name="P:CitizenFX.Core.Quaternion.Item(System.Int32)">
            <summary>
            Gets or sets the component at the specified index.
            </summary>
            <value>The value of the X, Y, Z, or W component, depending on the index.</value>
            <param name="index">The index of the component to access. Use 0 for the X component, 1 for the Y component, 2 for the Z component, and 3 for the W component.</param>
            <returns>The value of the component at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="index"/> is out of the range [0, 3].</exception>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Conjugate">
            <summary>
            Conjugates the quaternion.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Invert">
            <summary>
            Conjugates and renormalizes the quaternion.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Length">
            <summary>
            Calculates the length of the quaternion.
            </summary>
            <returns>The length of the quaternion.</returns>
            <remarks>
            <see cref="M:CitizenFX.Core.Quaternion.LengthSquared"/> may be preferred when only the relative length is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.LengthSquared">
            <summary>
            Calculates the squared length of the quaternion.
            </summary>
            <returns>The squared length of the quaternion.</returns>
            <remarks>
            This method may be preferred to <see cref="M:CitizenFX.Core.Quaternion.Length"/> when only a relative length is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Normalize">
            <summary>
            Converts the quaternion into a unit quaternion.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.ToArray">
            <summary>
            Creates an array containing the elements of the quaternion.
            </summary>
            <returns>A four-element array containing the components of the quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Add(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@)">
            <summary>
            Adds two quaternions.
            </summary>
            <param name="left">The first quaternion to add.</param>
            <param name="right">The second quaternion to add.</param>
            <param name="result">When the method completes, contains the sum of the two quaternions.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Add(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Adds two quaternions.
            </summary>
            <param name="left">The first quaternion to add.</param>
            <param name="right">The second quaternion to add.</param>
            <returns>The sum of the two quaternions.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Subtract(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@)">
            <summary>
            Subtracts two quaternions.
            </summary>
            <param name="left">The first quaternion to subtract.</param>
            <param name="right">The second quaternion to subtract.</param>
            <param name="result">When the method completes, contains the difference of the two quaternions.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Subtract(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Subtracts two quaternions.
            </summary>
            <param name="left">The first quaternion to subtract.</param>
            <param name="right">The second quaternion to subtract.</param>
            <returns>The difference of the two quaternions.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Multiply(CitizenFX.Core.Quaternion@,System.Single,CitizenFX.Core.Quaternion@)">
            <summary>
            Scales a quaternion by the given value.
            </summary>
            <param name="value">The quaternion to scale.</param>
            <param name="scale">The amount by which to scale the quaternion.</param>
            <param name="result">When the method completes, contains the scaled quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Multiply(CitizenFX.Core.Quaternion,System.Single)">
            <summary>
            Scales a quaternion by the given value.
            </summary>
            <param name="value">The quaternion to scale.</param>
            <param name="scale">The amount by which to scale the quaternion.</param>
            <returns>The scaled quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Multiply(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@)">
            <summary>
            Multiplies a quaternion by another.
            </summary>
            <param name="left">The first quaternion to multiply.</param>
            <param name="right">The second quaternion to multiply.</param>
            <param name="result">When the method completes, contains the multiplied quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Multiply(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Multiplies a quaternion by another.
            </summary>
            <param name="left">The first quaternion to multiply.</param>
            <param name="right">The second quaternion to multiply.</param>
            <returns>The multiplied quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Negate(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@)">
            <summary>
            Reverses the direction of a given quaternion.
            </summary>
            <param name="value">The quaternion to negate.</param>
            <param name="result">When the method completes, contains a quaternion facing in the opposite direction.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Negate(CitizenFX.Core.Quaternion)">
            <summary>
            Reverses the direction of a given quaternion.
            </summary>
            <param name="value">The quaternion to negate.</param>
            <returns>A quaternion facing in the opposite direction.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Barycentric(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,System.Single,System.Single,CitizenFX.Core.Quaternion@)">
            <summary>
            Returns a <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of a point specified in Barycentric coordinates relative to a 2D triangle.
            </summary>
            <param name="value1">A <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of vertex 1 of the triangle.</param>
            <param name="value2">A <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of vertex 2 of the triangle.</param>
            <param name="value3">A <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of vertex 3 of the triangle.</param>
            <param name="amount1">Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in <paramref name="value2"/>).</param>
            <param name="amount2">Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in <paramref name="value3"/>).</param>
            <param name="result">When the method completes, contains a new <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of the specified point.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Barycentric(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion,System.Single,System.Single)">
            <summary>
            Returns a <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of a point specified in Barycentric coordinates relative to a 2D triangle.
            </summary>
            <param name="value1">A <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of vertex 1 of the triangle.</param>
            <param name="value2">A <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of vertex 2 of the triangle.</param>
            <param name="value3">A <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of vertex 3 of the triangle.</param>
            <param name="amount1">Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in <paramref name="value2"/>).</param>
            <param name="amount2">Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in <paramref name="value3"/>).</param>
            <returns>A new <see cref="T:CitizenFX.Core.Quaternion"/> containing the 4D Cartesian coordinates of the specified point.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Conjugate(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@)">
            <summary>
            Conjugates a quaternion.
            </summary>
            <param name="value">The quaternion to conjugate.</param>
            <param name="result">When the method completes, contains the conjugated quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Conjugate(CitizenFX.Core.Quaternion)">
            <summary>
            Conjugates a quaternion.
            </summary>
            <param name="value">The quaternion to conjugate.</param>
            <returns>The conjugated quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Dot(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,System.Single@)">
            <summary>
            Calculates the dot product of two quaternions.
            </summary>
            <param name="left">First source quaternion.</param>
            <param name="right">Second source quaternion.</param>
            <param name="result">When the method completes, contains the dot product of the two quaternions.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Dot(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Calculates the dot product of two quaternions.
            </summary>
            <param name="left">First source quaternion.</param>
            <param name="right">Second source quaternion.</param>
            <returns>The dot product of the two quaternions.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Exponential(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@)">
            <summary>
            Exponentiates a quaternion.
            </summary>
            <param name="value">The quaternion to exponentiate.</param>
            <param name="result">When the method completes, contains the exponentiated quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Exponential(CitizenFX.Core.Quaternion)">
            <summary>
            Exponentiates a quaternion.
            </summary>
            <param name="value">The quaternion to exponentiate.</param>
            <returns>The exponentiated quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Invert(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@)">
            <summary>
            Conjugates and renormalizes the quaternion.
            </summary>
            <param name="value">The quaternion to conjugate and renormalize.</param>
            <param name="result">When the method completes, contains the conjugated and renormalized quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Invert(CitizenFX.Core.Quaternion)">
            <summary>
            Conjugates and renormalizes the quaternion.
            </summary>
            <param name="value">The quaternion to conjugate and renormalize.</param>
            <returns>The conjugated and renormalized quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Lerp(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,System.Single,CitizenFX.Core.Quaternion@)">
            <summary>
            Performs a linear interpolation between two quaternions.
            </summary>
            <param name="start">Start quaternion.</param>
            <param name="end">End quaternion.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the linear interpolation of the two quaternions.</param>
            <remarks>
            This method performs the linear interpolation based on the following formula.
            <code>start + (end - start) * amount</code>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Lerp(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion,System.Single)">
            <summary>
            Performs a linear interpolation between two quaternion.
            </summary>
            <param name="start">Start quaternion.</param>
            <param name="end">End quaternion.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The linear interpolation of the two quaternions.</returns>
            <remarks>
            This method performs the linear interpolation based on the following formula.
            <code>start + (end - start) * amount</code>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Logarithm(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@)">
            <summary>
            Calculates the natural logarithm of the specified quaternion.
            </summary>
            <param name="value">The quaternion whose logarithm will be calculated.</param>
            <param name="result">When the method completes, contains the natural logarithm of the quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Logarithm(CitizenFX.Core.Quaternion)">
            <summary>
            Calculates the natural logarithm of the specified quaternion.
            </summary>
            <param name="value">The quaternion whose logarithm will be calculated.</param>
            <returns>The natural logarithm of the quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Normalize(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@)">
            <summary>
            Converts the quaternion into a unit quaternion.
            </summary>
            <param name="value">The quaternion to normalize.</param>
            <param name="result">When the method completes, contains the normalized quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Normalize(CitizenFX.Core.Quaternion)">
            <summary>
            Converts the quaternion into a unit quaternion.
            </summary>
            <param name="value">The quaternion to normalize.</param>
            <returns>The normalized quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationAxis(CitizenFX.Core.Vector3@,System.Single,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a quaternion given a rotation and an axis.
            </summary>
            <param name="axis">The axis of rotation.</param>
            <param name="angle">The angle of rotation.</param>
            <param name="result">When the method completes, contains the newly created quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationAxis(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Creates a quaternion given a rotation and an axis.
            </summary>
            <param name="axis">The axis of rotation.</param>
            <param name="angle">The angle of rotation.</param>
            <returns>The newly created quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationMatrix(CitizenFX.Core.Matrix@,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a quaternion given a rotation matrix.
            </summary>
            <param name="matrix">The rotation matrix.</param>
            <param name="result">When the method completes, contains the newly created quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationMatrix(CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a quaternion given a rotation matrix.
            </summary>
            <param name="matrix">The rotation matrix.</param>
            <param name="result">When the method completes, contains the newly created quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.LookAtLH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a left-handed, look-at quaternion.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <param name="result">When the method completes, contains the created look-at quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.LookAtLH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a left-handed, look-at quaternion.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <returns>The created look-at quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationLookAtLH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a left-handed, look-at quaternion.
            </summary>
            <param name="forward">The camera's forward direction.</param>
            <param name="up">The camera's up vector.</param>
            <param name="result">When the method completes, contains the created look-at quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationLookAtLH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a left-handed, look-at quaternion.
            </summary>
            <param name="forward">The camera's forward direction.</param>
            <param name="up">The camera's up vector.</param>
            <returns>The created look-at quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.LookAtRH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a right-handed, look-at quaternion.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <param name="result">When the method completes, contains the created look-at quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.LookAtRH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a right-handed, look-at quaternion.
            </summary>
            <param name="eye">The position of the viewer's eye.</param>
            <param name="target">The camera look-at target.</param>
            <param name="up">The camera's up vector.</param>
            <returns>The created look-at quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationLookAtRH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a right-handed, look-at quaternion.
            </summary>
            <param name="forward">The camera's forward direction.</param>
            <param name="up">The camera's up vector.</param>
            <param name="result">When the method completes, contains the created look-at quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationLookAtRH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a right-handed, look-at quaternion.
            </summary>
            <param name="forward">The camera's forward direction.</param>
            <param name="up">The camera's up vector.</param>
            <returns>The created look-at quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.BillboardLH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a left-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <param name="result">When the method completes, contains the created billboard quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.BillboardLH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a left-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <returns>The created billboard quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.BillboardRH(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a right-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <param name="result">When the method completes, contains the created billboard quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.BillboardRH(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Creates a right-handed spherical billboard that rotates around a specified object position.
            </summary>
            <param name="objectPosition">The position of the object around which the billboard will rotate.</param>
            <param name="cameraPosition">The position of the camera.</param>
            <param name="cameraUpVector">The up vector of the camera.</param>
            <param name="cameraForwardVector">The forward vector of the camera.</param>
            <returns>The created billboard quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationMatrix(CitizenFX.Core.Matrix)">
            <summary>
            Creates a quaternion given a rotation matrix.
            </summary>
            <param name="matrix">The rotation matrix.</param>
            <returns>The newly created quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationYawPitchRoll(System.Single,System.Single,System.Single,CitizenFX.Core.Quaternion@)">
            <summary>
            Creates a quaternion given a yaw, pitch, and roll value.
            </summary>
            <param name="yaw">The yaw of rotation.</param>
            <param name="pitch">The pitch of rotation.</param>
            <param name="roll">The roll of rotation.</param>
            <param name="result">When the method completes, contains the newly created quaternion.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.RotationYawPitchRoll(System.Single,System.Single,System.Single)">
            <summary>
            Creates a quaternion given a yaw, pitch, and roll value.
            </summary>
            <param name="yaw">The yaw of rotation.</param>
            <param name="pitch">The pitch of rotation.</param>
            <param name="roll">The roll of rotation.</param>
            <returns>The newly created quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Slerp(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,System.Single,CitizenFX.Core.Quaternion@)">
            <summary>
            Interpolates between two quaternions, using spherical linear interpolation.
            </summary>
            <param name="start">Start quaternion.</param>
            <param name="end">End quaternion.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the spherical linear interpolation of the two quaternions.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Slerp(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion,System.Single)">
            <summary>
            Interpolates between two quaternions, using spherical linear interpolation.
            </summary>
            <param name="start">Start quaternion.</param>
            <param name="end">End quaternion.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The spherical linear interpolation of the two quaternions.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Squad(CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Quaternion@,System.Single,CitizenFX.Core.Quaternion@)">
            <summary>
            Interpolates between quaternions, using spherical quadrangle interpolation.
            </summary>
            <param name="value1">First source quaternion.</param>
            <param name="value2">Second source quaternion.</param>
            <param name="value3">Third source quaternion.</param>
            <param name="value4">Fourth source quaternion.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of interpolation.</param>
            <param name="result">When the method completes, contains the spherical quadrangle interpolation of the quaternions.</param>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Squad(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion,System.Single)">
            <summary>
            Interpolates between quaternions, using spherical quadrangle interpolation.
            </summary>
            <param name="value1">First source quaternion.</param>
            <param name="value2">Second source quaternion.</param>
            <param name="value3">Third source quaternion.</param>
            <param name="value4">Fourth source quaternion.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of interpolation.</param>
            <returns>The spherical quadrangle interpolation of the quaternions.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.SquadSetup(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Sets up control points for spherical quadrangle interpolation.
            </summary>
            <param name="value1">First source quaternion.</param>
            <param name="value2">Second source quaternion.</param>
            <param name="value3">Third source quaternion.</param>
            <param name="value4">Fourth source quaternion.</param>
            <returns>An array of three quaternions that represent control points for spherical quadrangle interpolation.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.op_Addition(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Adds two quaternions.
            </summary>
            <param name="left">The first quaternion to add.</param>
            <param name="right">The second quaternion to add.</param>
            <returns>The sum of the two quaternions.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.op_Subtraction(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Subtracts two quaternions.
            </summary>
            <param name="left">The first quaternion to subtract.</param>
            <param name="right">The second quaternion to subtract.</param>
            <returns>The difference of the two quaternions.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.op_UnaryNegation(CitizenFX.Core.Quaternion)">
            <summary>
            Reverses the direction of a given quaternion.
            </summary>
            <param name="value">The quaternion to negate.</param>
            <returns>A quaternion facing in the opposite direction.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.op_Multiply(System.Single,CitizenFX.Core.Quaternion)">
            <summary>
            Scales a quaternion by the given value.
            </summary>
            <param name="value">The quaternion to scale.</param>
            <param name="scale">The amount by which to scale the quaternion.</param>
            <returns>The scaled quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.op_Multiply(CitizenFX.Core.Quaternion,System.Single)">
            <summary>
            Scales a quaternion by the given value.
            </summary>
            <param name="value">The quaternion to scale.</param>
            <param name="scale">The amount by which to scale the quaternion.</param>
            <returns>The scaled quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.op_Multiply(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Multiplies a quaternion by another.
            </summary>
            <param name="left">The first quaternion to multiply.</param>
            <param name="right">The second quaternion to multiply.</param>
            <returns>The multiplied quaternion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.op_Equality(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Tests for equality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has the same value as <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.op_Inequality(CitizenFX.Core.Quaternion,CitizenFX.Core.Quaternion)">
            <summary>
            Tests for inequality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has a different value than <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.ToString(System.String)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.ToString(System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Equals(CitizenFX.Core.Quaternion@)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Quaternion"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Quaternion"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:CitizenFX.Core.Quaternion"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Equals(CitizenFX.Core.Quaternion)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Quaternion"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Quaternion"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:CitizenFX.Core.Quaternion"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Quaternion.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:CitizenFX.Core.Vector2">
            <summary>
            Represents a two dimensional mathematical vector.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector2.Zero">
            <summary>
            A <see cref="T:CitizenFX.Core.Vector2"/> with all of its components set to zero.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector2.UnitX">
            <summary>
            The X unit <see cref="T:CitizenFX.Core.Vector2"/> (1, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector2.UnitY">
            <summary>
            The Y unit <see cref="T:CitizenFX.Core.Vector2"/> (0, 1).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector2.One">
            <summary>
            A <see cref="T:CitizenFX.Core.Vector2"/> with all of its components set to one.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector2.X">
            <summary>
            The X component of the vector.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector2.Y">
            <summary>
            The Y component of the vector.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Vector2.#ctor(CitizenFX.Core.Vector2@)">
            <summary>
            Copies to a new instance of the <see cref="T:CitizenFX.Core.Vector2"/> struct.
            </summary>
            <param name="value">The value that will be assigned to all components.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector2"/> struct.
            </summary>
            <param name="value">The value that will be assigned to all components.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.#ctor(System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector2"/> struct.
            </summary>
            <param name="x">Initial value for the X component of the vector.</param>
            <param name="y">Initial value for the Y component of the vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.#ctor(System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector2"/> struct.
            </summary>
            <param name="values">The values to assign to the X and Y components of the vector. This must be an array with two elements.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="values"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="values"/> contains more or less than two elements.</exception>
        </member>
        <member name="P:CitizenFX.Core.Vector2.IsNormalized">
            <summary>
            Gets a value indicting whether this instance is normalized.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Vector2.IsZero">
            <summary>
            Gets a value indicting whether this vector is zero
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Vector2.Item(System.Int32)">
            <summary>
            Gets or sets the component at the specified index.
            </summary>
            <value>The value of the X or Y component, depending on the index.</value>
            <param name="index">The index of the component to access. Use 0 for the X component and 1 for the Y component.</param>
            <returns>The value of the component at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="index"/> is out of the range [0, 1].</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Length">
            <summary>
            Calculates the length of the vector.
            </summary>
            <returns>The length of the vector.</returns>
            <remarks>
            <see cref="M:CitizenFX.Core.Vector2.LengthSquared"/> may be preferred when only the relative length is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.LengthSquared">
            <summary>
            Calculates the squared length of the vector.
            </summary>
            <returns>The squared length of the vector.</returns>
            <remarks>
            This method may be preferred to <see cref="M:CitizenFX.Core.Vector2.Length"/> when only a relative length is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Normalize">
            <summary>
            Converts the vector into a unit vector.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Vector2.ToArray">
            <summary>
            Creates an array containing the elements of the vector.
            </summary>
            <returns>A two-element array containing the components of the vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Add(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Adds two vectors.
            </summary>
            <param name="left">The first vector to add.</param>
            <param name="right">The second vector to add.</param>
            <param name="result">When the method completes, contains the sum of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Add(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Adds two vectors.
            </summary>
            <param name="left">The first vector to add.</param>
            <param name="right">The second vector to add.</param>
            <returns>The sum of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Add(CitizenFX.Core.Vector2@,System.Single@,CitizenFX.Core.Vector2@)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be added to elements</param>
            <param name="result">The vector with added scalar for each element.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Add(CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be added to elements</param>
            <returns>The vector with added scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Subtract(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Subtracts two vectors.
            </summary>
            <param name="left">The first vector to subtract.</param>
            <param name="right">The second vector to subtract.</param>
            <param name="result">When the method completes, contains the difference of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Subtract(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Subtracts two vectors.
            </summary>
            <param name="left">The first vector to subtract.</param>
            <param name="right">The second vector to subtract.</param>
            <returns>The difference of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Subtract(CitizenFX.Core.Vector2@,System.Single@,CitizenFX.Core.Vector2@)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be subtraced from elements</param>
            <param name="result">The vector with subtracted scalar for each element.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Subtract(CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be subtraced from elements</param>
            <returns>The vector with subtracted scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Subtract(System.Single@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The scalar value to be subtraced from elements</param>
            <param name="right">The input vector</param>
            <param name="result">The vector with subtracted scalar for each element.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Subtract(System.Single,CitizenFX.Core.Vector2)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The scalar value to be subtraced from elements</param>
            <param name="right">The input vector</param>
            <returns>The vector with subtracted scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Multiply(CitizenFX.Core.Vector2@,System.Single,CitizenFX.Core.Vector2@)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="result">When the method completes, contains the scaled vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Multiply(CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Multiply(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Multiplies a vector with another by performing component-wise multiplication.
            </summary>
            <param name="left">The first vector to multiply.</param>
            <param name="right">The second vector to multiply.</param>
            <param name="result">When the method completes, contains the multiplied vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Multiply(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Multiplies a vector with another by performing component-wise multiplication.
            </summary>
            <param name="left">The first vector to multiply.</param>
            <param name="right">The second vector to multiply.</param>
            <returns>The multiplied vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Divide(CitizenFX.Core.Vector2@,System.Single,CitizenFX.Core.Vector2@)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="result">When the method completes, contains the scaled vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Divide(CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Divide(System.Single,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="value">The vector to scale.</param>
            <param name="result">When the method completes, contains the scaled vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Divide(System.Single,CitizenFX.Core.Vector2)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Negate(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Reverses the direction of a given vector.
            </summary>
            <param name="value">The vector to negate.</param>
            <param name="result">When the method completes, contains a vector facing in the opposite direction.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Negate(CitizenFX.Core.Vector2)">
            <summary>
            Reverses the direction of a given vector.
            </summary>
            <param name="value">The vector to negate.</param>
            <returns>A vector facing in the opposite direction.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Barycentric(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single,System.Single,CitizenFX.Core.Vector2@)">
            <summary>
            Returns a <see cref="T:CitizenFX.Core.Vector2"/> containing the 2D Cartesian coordinates of a point specified in Barycentric coordinates relative to a 2D triangle.
            </summary>
            <param name="value1">A <see cref="T:CitizenFX.Core.Vector2"/> containing the 2D Cartesian coordinates of vertex 1 of the triangle.</param>
            <param name="value2">A <see cref="T:CitizenFX.Core.Vector2"/> containing the 2D Cartesian coordinates of vertex 2 of the triangle.</param>
            <param name="value3">A <see cref="T:CitizenFX.Core.Vector2"/> containing the 2D Cartesian coordinates of vertex 3 of the triangle.</param>
            <param name="amount1">Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in <paramref name="value2"/>).</param>
            <param name="amount2">Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in <paramref name="value3"/>).</param>
            <param name="result">When the method completes, contains the 2D Cartesian coordinates of the specified point.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Barycentric(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,System.Single,System.Single)">
            <summary>
            Returns a <see cref="T:CitizenFX.Core.Vector2"/> containing the 2D Cartesian coordinates of a point specified in Barycentric coordinates relative to a 2D triangle.
            </summary>
            <param name="value1">A <see cref="T:CitizenFX.Core.Vector2"/> containing the 2D Cartesian coordinates of vertex 1 of the triangle.</param>
            <param name="value2">A <see cref="T:CitizenFX.Core.Vector2"/> containing the 2D Cartesian coordinates of vertex 2 of the triangle.</param>
            <param name="value3">A <see cref="T:CitizenFX.Core.Vector2"/> containing the 2D Cartesian coordinates of vertex 3 of the triangle.</param>
            <param name="amount1">Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in <paramref name="value2"/>).</param>
            <param name="amount2">Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in <paramref name="value3"/>).</param>
            <returns>A new <see cref="T:CitizenFX.Core.Vector2"/> containing the 2D Cartesian coordinates of the specified point.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Clamp(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Restricts a value to be within a specified range.
            </summary>
            <param name="value">The value to clamp.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="result">When the method completes, contains the clamped value.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Clamp(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Restricts a value to be within a specified range.
            </summary>
            <param name="value">The value to clamp.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <returns>The clamped value.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Saturate">
            <summary>
            Saturates this instance in the range [0,1]
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Distance(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single@)">
            <summary>
            Calculates the distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <param name="result">When the method completes, contains the distance between the two vectors.</param>
            <remarks>
            <see cref="M:CitizenFX.Core.Vector2.DistanceSquared(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single@)"/> may be preferred when only the relative distance is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Distance(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Calculates the distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <returns>The distance between the two vectors.</returns>
            <remarks>
            <see cref="M:CitizenFX.Core.Vector2.DistanceSquared(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)"/> may be preferred when only the relative distance is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.DistanceSquared(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single@)">
            <summary>
            Calculates the squared distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector</param>
            <param name="result">When the method completes, contains the squared distance between the two vectors.</param>
            <remarks>Distance squared is the value before taking the square root. 
            Distance squared can often be used in place of distance if relative comparisons are being made. 
            For example, consider three points A, B, and C. To determine whether B or C is further from A, 
            compare the distance between A and B to the distance between A and C. Calculating the two distances 
            involves two square roots, which are computationally expensive. However, using distance squared 
            provides the same information and avoids calculating two square roots.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.DistanceSquared(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Calculates the squared distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <returns>The squared distance between the two vectors.</returns>
            <remarks>Distance squared is the value before taking the square root. 
            Distance squared can often be used in place of distance if relative comparisons are being made. 
            For example, consider three points A, B, and C. To determine whether B or C is further from A, 
            compare the distance between A and B to the distance between A and C. Calculating the two distances 
            involves two square roots, which are computationally expensive. However, using distance squared 
            provides the same information and avoids calculating two square roots.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Dot(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single@)">
            <summary>
            Calculates the dot product of two vectors.
            </summary>
            <param name="left">First source vector.</param>
            <param name="right">Second source vector.</param>
            <param name="result">When the method completes, contains the dot product of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Dot(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Calculates the dot product of two vectors.
            </summary>
            <param name="left">First source vector.</param>
            <param name="right">Second source vector.</param>
            <returns>The dot product of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Normalize(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Converts the vector into a unit vector.
            </summary>
            <param name="value">The vector to normalize.</param>
            <param name="result">When the method completes, contains the normalized vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Normalize(CitizenFX.Core.Vector2)">
            <summary>
            Converts the vector into a unit vector.
            </summary>
            <param name="value">The vector to normalize.</param>
            <returns>The normalized vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Lerp(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single,CitizenFX.Core.Vector2@)">
            <summary>
            Performs a linear interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the linear interpolation of the two vectors.</param>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Lerp(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Performs a linear interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The linear interpolation of the two vectors.</returns>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.SmoothStep(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single,CitizenFX.Core.Vector2@)">
            <summary>
            Performs a cubic interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the cubic interpolation of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.SmoothStep(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Performs a cubic interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The cubic interpolation of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Hermite(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single,CitizenFX.Core.Vector2@)">
            <summary>
            Performs a Hermite spline interpolation.
            </summary>
            <param name="value1">First source position vector.</param>
            <param name="tangent1">First source tangent vector.</param>
            <param name="value2">Second source position vector.</param>
            <param name="tangent2">Second source tangent vector.</param>
            <param name="amount">Weighting factor.</param>
            <param name="result">When the method completes, contains the result of the Hermite spline interpolation.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Hermite(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Performs a Hermite spline interpolation.
            </summary>
            <param name="value1">First source position vector.</param>
            <param name="tangent1">First source tangent vector.</param>
            <param name="value2">Second source position vector.</param>
            <param name="tangent2">Second source tangent vector.</param>
            <param name="amount">Weighting factor.</param>
            <returns>The result of the Hermite spline interpolation.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.CatmullRom(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,System.Single,CitizenFX.Core.Vector2@)">
            <summary>
            Performs a Catmull-Rom interpolation using the specified positions.
            </summary>
            <param name="value1">The first position in the interpolation.</param>
            <param name="value2">The second position in the interpolation.</param>
            <param name="value3">The third position in the interpolation.</param>
            <param name="value4">The fourth position in the interpolation.</param>
            <param name="amount">Weighting factor.</param>
            <param name="result">When the method completes, contains the result of the Catmull-Rom interpolation.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.CatmullRom(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Performs a Catmull-Rom interpolation using the specified positions.
            </summary>
            <param name="value1">The first position in the interpolation.</param>
            <param name="value2">The second position in the interpolation.</param>
            <param name="value3">The third position in the interpolation.</param>
            <param name="value4">The fourth position in the interpolation.</param>
            <param name="amount">Weighting factor.</param>
            <returns>A vector that is the result of the Catmull-Rom interpolation.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Max(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Returns a vector containing the largest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <param name="result">When the method completes, contains an new vector composed of the largest components of the source vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Max(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Returns a vector containing the largest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <returns>A vector containing the largest components of the source vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Min(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Returns a vector containing the smallest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <param name="result">When the method completes, contains an new vector composed of the smallest components of the source vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Min(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Returns a vector containing the smallest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <returns>A vector containing the smallest components of the source vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Reflect(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)">
            <summary>
            Returns the reflection of a vector off a surface that has the specified normal. 
            </summary>
            <param name="vector">The source vector.</param>
            <param name="normal">Normal of the surface.</param>
            <param name="result">When the method completes, contains the reflected vector.</param>
            <remarks>Reflect only gives the direction of a reflection off a surface, it does not determine 
            whether the original vector was close enough to the surface to hit it.</remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Reflect(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Returns the reflection of a vector off a surface that has the specified normal. 
            </summary>
            <param name="vector">The source vector.</param>
            <param name="normal">Normal of the surface.</param>
            <returns>The reflected vector.</returns>
            <remarks>Reflect only gives the direction of a reflection off a surface, it does not determine 
            whether the original vector was close enough to the surface to hit it.</remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Orthogonalize(CitizenFX.Core.Vector2[],CitizenFX.Core.Vector2[])">
            <summary>
            Orthogonalizes a list of vectors.
            </summary>
            <param name="destination">The list of orthogonalized vectors.</param>
            <param name="source">The list of vectors to orthogonalize.</param>
            <remarks>
            <para>Orthogonalization is the process of making all vectors orthogonal to each other. This
            means that any given vector in the list will be orthogonal to any other given vector in the
            list.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting vectors
            tend to be numerically unstable. The numeric stability decreases according to the vectors
            position in the list so that the first vector is the most stable and the last vector is the
            least stable.</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Orthonormalize(CitizenFX.Core.Vector2[],CitizenFX.Core.Vector2[])">
            <summary>
            Orthonormalizes a list of vectors.
            </summary>
            <param name="destination">The list of orthonormalized vectors.</param>
            <param name="source">The list of vectors to orthonormalize.</param>
            <remarks>
            <para>Orthonormalization is the process of making all vectors orthogonal to each
            other and making all vectors of unit length. This means that any given vector will
            be orthogonal to any other given vector in the list.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting vectors
            tend to be numerically unstable. The numeric stability decreases according to the vectors
            position in the list so that the first vector is the most stable and the last vector is the
            least stable.</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Transform(CitizenFX.Core.Vector2@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector2@)">
            <summary>
            Transforms a 2D vector by the given <see cref="T:CitizenFX.Core.Quaternion"/> rotation.
            </summary>
            <param name="vector">The vector to rotate.</param>
            <param name="rotation">The <see cref="T:CitizenFX.Core.Quaternion"/> rotation to apply.</param>
            <param name="result">When the method completes, contains the transformed <see cref="T:CitizenFX.Core.Vector4"/>.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Transform(CitizenFX.Core.Vector2,CitizenFX.Core.Quaternion)">
            <summary>
            Transforms a 2D vector by the given <see cref="T:CitizenFX.Core.Quaternion"/> rotation.
            </summary>
            <param name="vector">The vector to rotate.</param>
            <param name="rotation">The <see cref="T:CitizenFX.Core.Quaternion"/> rotation to apply.</param>
            <returns>The transformed <see cref="T:CitizenFX.Core.Vector4"/>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Transform(CitizenFX.Core.Vector2[],CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector2[])">
            <summary>
            Transforms an array of vectors by the given <see cref="T:CitizenFX.Core.Quaternion"/> rotation.
            </summary>
            <param name="source">The array of vectors to transform.</param>
            <param name="rotation">The <see cref="T:CitizenFX.Core.Quaternion"/> rotation to apply.</param>
            <param name="destination">The array for which the transformed vectors are stored.
            This array may be the same array as <paramref name="source"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Transform(CitizenFX.Core.Vector2@,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector4@)">
            <summary>
            Transforms a 2D vector by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="vector">The source vector.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="result">When the method completes, contains the transformed <see cref="T:CitizenFX.Core.Vector4"/>.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Transform(CitizenFX.Core.Vector2,CitizenFX.Core.Matrix)">
            <summary>
            Transforms a 2D vector by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="vector">The source vector.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <returns>The transformed <see cref="T:CitizenFX.Core.Vector4"/>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Transform(CitizenFX.Core.Vector2[],CitizenFX.Core.Matrix@,CitizenFX.Core.Vector4[])">
            <summary>
            Transforms an array of 2D vectors by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="source">The array of vectors to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="destination">The array for which the transformed vectors are stored.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector2.TransformCoordinate(CitizenFX.Core.Vector2@,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector2@)">
            <summary>
            Performs a coordinate transformation using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="coordinate">The coordinate vector to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="result">When the method completes, contains the transformed coordinates.</param>
            <remarks>
            A coordinate transform performs the transformation with the assumption that the w component
            is one. The four dimensional vector obtained from the transformation operation has each
            component in the vector divided by the w component. This forces the w component to be one and
            therefore makes the vector homogeneous. The homogeneous vector is often preferred when working
            with coordinates as the w component can safely be ignored.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.TransformCoordinate(CitizenFX.Core.Vector2,CitizenFX.Core.Matrix)">
            <summary>
            Performs a coordinate transformation using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="coordinate">The coordinate vector to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <returns>The transformed coordinates.</returns>
            <remarks>
            A coordinate transform performs the transformation with the assumption that the w component
            is one. The four dimensional vector obtained from the transformation operation has each
            component in the vector divided by the w component. This forces the w component to be one and
            therefore makes the vector homogeneous. The homogeneous vector is often preferred when working
            with coordinates as the w component can safely be ignored.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.TransformCoordinate(CitizenFX.Core.Vector2[],CitizenFX.Core.Matrix@,CitizenFX.Core.Vector2[])">
            <summary>
            Performs a coordinate transformation on an array of vectors using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="source">The array of coordinate vectors to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="destination">The array for which the transformed vectors are stored.
            This array may be the same array as <paramref name="source"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
            <remarks>
            A coordinate transform performs the transformation with the assumption that the w component
            is one. The four dimensional vector obtained from the transformation operation has each
            component in the vector divided by the w component. This forces the w component to be one and
            therefore makes the vector homogeneous. The homogeneous vector is often preferred when working
            with coordinates as the w component can safely be ignored.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.TransformNormal(CitizenFX.Core.Vector2@,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector2@)">
            <summary>
            Performs a normal transformation using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="normal">The normal vector to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="result">When the method completes, contains the transformed normal.</param>
            <remarks>
            A normal transform performs the transformation with the assumption that the w component
            is zero. This causes the fourth row and fourth column of the matrix to be unused. The
            end result is a vector that is not translated, but all other transformation properties
            apply. This is often preferred for normal vectors as normals purely represent direction
            rather than location because normal vectors should not be translated.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.TransformNormal(CitizenFX.Core.Vector2,CitizenFX.Core.Matrix)">
            <summary>
            Performs a normal transformation using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="normal">The normal vector to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <returns>The transformed normal.</returns>
            <remarks>
            A normal transform performs the transformation with the assumption that the w component
            is zero. This causes the fourth row and fourth column of the matrix to be unused. The
            end result is a vector that is not translated, but all other transformation properties
            apply. This is often preferred for normal vectors as normals purely represent direction
            rather than location because normal vectors should not be translated.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.TransformNormal(CitizenFX.Core.Vector2[],CitizenFX.Core.Matrix@,CitizenFX.Core.Vector2[])">
            <summary>
            Performs a normal transformation on an array of vectors using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="source">The array of normal vectors to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="destination">The array for which the transformed vectors are stored.
            This array may be the same array as <paramref name="source"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
            <remarks>
            A normal transform performs the transformation with the assumption that the w component
            is zero. This causes the fourth row and fourth column of the matrix to be unused. The
            end result is a vector that is not translated, but all other transformation properties
            apply. This is often preferred for normal vectors as normals purely represent direction
            rather than location because normal vectors should not be translated.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Addition(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Adds two vectors.
            </summary>
            <param name="left">The first vector to add.</param>
            <param name="right">The second vector to add.</param>
            <returns>The sum of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Multiply(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Multiplies a vector with another by performing component-wise multiplication equivalent to <see cref="M:CitizenFX.Core.Vector2.Multiply(CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@,CitizenFX.Core.Vector2@)"/>.
            </summary>
            <param name="left">The first vector to multiply.</param>
            <param name="right">The second vector to multiply.</param>
            <returns>The multiplication of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_UnaryPlus(CitizenFX.Core.Vector2)">
            <summary>
            Assert a vector (return it unchanged).
            </summary>
            <param name="value">The vector to assert (unchanged).</param>
            <returns>The asserted (unchanged) vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Subtraction(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Subtracts two vectors.
            </summary>
            <param name="left">The first vector to subtract.</param>
            <param name="right">The second vector to subtract.</param>
            <returns>The difference of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_UnaryNegation(CitizenFX.Core.Vector2)">
            <summary>
            Reverses the direction of a given vector.
            </summary>
            <param name="value">The vector to negate.</param>
            <returns>A vector facing in the opposite direction.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Multiply(System.Single,CitizenFX.Core.Vector2)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Multiply(CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Division(CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Division(System.Single,CitizenFX.Core.Vector2)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="value">The vector to scale.</param>  
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Division(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Addition(CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be added on elements</param>
            <returns>The vector with added scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Addition(System.Single,CitizenFX.Core.Vector2)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be added on elements</param>
            <returns>The vector with added scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Subtraction(CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be subtraced from elements</param>
            <returns>The vector with subtraced scalar from each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Subtraction(System.Single,CitizenFX.Core.Vector2)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be subtraced from elements</param>
            <returns>The vector with subtraced scalar from each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Equality(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Tests for equality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has the same value as <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Inequality(CitizenFX.Core.Vector2,CitizenFX.Core.Vector2)">
            <summary>
            Tests for inequality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has a different value than <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Explicit(CitizenFX.Core.Vector2)~CitizenFX.Core.Vector3">
            <summary>
            Performs an explicit conversion from <see cref="T:CitizenFX.Core.Vector2"/> to <see cref="T:CitizenFX.Core.Vector3"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.op_Explicit(CitizenFX.Core.Vector2)~CitizenFX.Core.Vector4">
            <summary>
            Performs an explicit conversion from <see cref="T:CitizenFX.Core.Vector2"/> to <see cref="T:CitizenFX.Core.Vector4"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.ToString(System.String)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.ToString(System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Equals(CitizenFX.Core.Vector2@)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Vector2"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Vector2"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:CitizenFX.Core.Vector2"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Equals(CitizenFX.Core.Vector2)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Vector2"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Vector2"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:CitizenFX.Core.Vector2"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector2.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:CitizenFX.Core.Vector3">
            <summary>
            Represents a three dimensional mathematical vector.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.Zero">
            <summary>
            A <see cref="T:CitizenFX.Core.Vector3"/> with all of its components set to zero.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.UnitX">
            <summary>
            The X unit <see cref="T:CitizenFX.Core.Vector3"/> (1, 0, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.UnitY">
            <summary>
            The Y unit <see cref="T:CitizenFX.Core.Vector3"/> (0, 1, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.UnitZ">
            <summary>
            The Z unit <see cref="T:CitizenFX.Core.Vector3"/> (0, 0, 1).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.One">
            <summary>
            A <see cref="T:CitizenFX.Core.Vector3"/> with all of its components set to one.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.Up">
            <summary>
            A unit <see cref="T:CitizenFX.Core.Vector3"/> designating up (0, 0, 1).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.Down">
            <summary>
            A unit <see cref="T:CitizenFX.Core.Vector3"/> designating down (0, 0, -1).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.Left">
            <summary>
            A unit <see cref="T:CitizenFX.Core.Vector3"/> designating left (-1, 0, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.Right">
            <summary>
            A unit <see cref="T:CitizenFX.Core.Vector3"/> designating right (1, 0, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.ForwardRH">
            <summary>
            A unit <see cref="T:CitizenFX.Core.Vector3"/> designating forward in a right-handed coordinate system (0, 1, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.ForwardLH">
            <summary>
            A unit <see cref="T:CitizenFX.Core.Vector3"/> designating forward in a left-handed coordinate system (0, -1, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.BackwardRH">
            <summary>
            A unit <see cref="T:CitizenFX.Core.Vector3"/> designating backward in a right-handed coordinate system (0, -1, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.BackwardLH">
            <summary>
            A unit <see cref="T:CitizenFX.Core.Vector3"/> designating backward in a left-handed coordinate system (0, 1, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.Forward">
            <inheritdoc cref="F:CitizenFX.Core.Vector3.ForwardRH"/>
        </member>
        <member name="F:CitizenFX.Core.Vector3.Backward">
            <inheritdoc cref="F:CitizenFX.Core.Vector3.BackwardRH"/>
        </member>
        <member name="F:CitizenFX.Core.Vector3.X">
            <summary>
            The X component of the vector.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.Y">
            <summary>
            The Y component of the vector.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector3.Z">
            <summary>
            The Z component of the vector.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Vector3.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector3"/> struct.
            </summary>
            <param name="value">The value that will be assigned to all components.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.#ctor(System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector3"/> struct.
            </summary>
            <param name="x">Initial value for the X component of the vector.</param>
            <param name="y">Initial value for the Y component of the vector.</param>
            <param name="z">Initial value for the Z component of the vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.#ctor(CitizenFX.Core.Vector2,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector3"/> struct.
            </summary>
            <param name="value">A vector containing the values with which to initialize the X and Y components.</param>
            <param name="z">Initial value for the Z component of the vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.#ctor(System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector3"/> struct.
            </summary>
            <param name="values">The values to assign to the X, Y, and Z components of the vector. This must be an array with three elements.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="values"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="values"/> contains more or less than three elements.</exception>
        </member>
        <member name="P:CitizenFX.Core.Vector3.IsNormalized">
            <summary>
            Gets a value indicting whether this instance is normalized.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Vector3.IsZero">
            <summary>
            Gets a value indicting whether this vector is zero
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Vector3.Item(System.Int32)">
            <summary>
            Gets or sets the component at the specified index.
            </summary>
            <value>The value of the X, Y, or Z component, depending on the index.</value>
            <param name="index">The index of the component to access. Use 0 for the X component, 1 for the Y component, and 2 for the Z component.</param>
            <returns>The value of the component at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="index"/> is out of the range [0, 2].</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Length">
            <summary>
            Calculates the length of the vector.
            </summary>
            <returns>The length of the vector.</returns>
            <remarks>
            <see cref="M:CitizenFX.Core.Vector3.LengthSquared"/> may be preferred when only the relative length is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.LengthSquared">
            <summary>
            Calculates the squared length of the vector.
            </summary>
            <returns>The squared length of the vector.</returns>
            <remarks>
            This method may be preferred to <see cref="M:CitizenFX.Core.Vector3.Length"/> when only a relative length is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Normalize">
            <summary>
            Converts the vector into a unit vector.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Vector3.ToArray">
            <summary>
            Creates an array containing the elements of the vector.
            </summary>
            <returns>A three-element array containing the components of the vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Add(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Adds two vectors.
            </summary>
            <param name="left">The first vector to add.</param>
            <param name="right">The second vector to add.</param>
            <param name="result">When the method completes, contains the sum of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Add(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Adds two vectors.
            </summary>
            <param name="left">The first vector to add.</param>
            <param name="right">The second vector to add.</param>
            <returns>The sum of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Add(CitizenFX.Core.Vector3@,System.Single@,CitizenFX.Core.Vector3@)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be added to elements</param>
            <param name="result">The vector with added scalar for each element.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Add(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be added to elements</param>
            <returns>The vector with added scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Subtract(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Subtracts two vectors.
            </summary>
            <param name="left">The first vector to subtract.</param>
            <param name="right">The second vector to subtract.</param>
            <param name="result">When the method completes, contains the difference of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Subtract(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Subtracts two vectors.
            </summary>
            <param name="left">The first vector to subtract.</param>
            <param name="right">The second vector to subtract.</param>
            <returns>The difference of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Subtract(CitizenFX.Core.Vector3@,System.Single@,CitizenFX.Core.Vector3@)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be subtraced from elements</param>
            <param name="result">The vector with subtracted scalar for each element.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Subtract(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be subtraced from elements</param>
            <returns>The vector with subtracted scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Subtract(System.Single@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The scalar value to be subtraced from elements</param>
            <param name="right">The input vector.</param>
            <param name="result">The vector with subtracted scalar for each element.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Subtract(System.Single,CitizenFX.Core.Vector3)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The scalar value to be subtraced from elements</param>
            <param name="right">The input vector.</param>
            <returns>The vector with subtracted scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Multiply(CitizenFX.Core.Vector3@,System.Single,CitizenFX.Core.Vector3@)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="result">When the method completes, contains the scaled vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Multiply(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Multiply(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Multiply a vector with another by performing component-wise multiplication.
            </summary>
            <param name="left">The first vector to multiply.</param>
            <param name="right">The second vector to multiply.</param>
            <param name="result">When the method completes, contains the multiplied vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Multiply(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Multiply a vector with another by performing component-wise multiplication.
            </summary>
            <param name="left">The first vector to Multiply.</param>
            <param name="right">The second vector to multiply.</param>
            <returns>The multiplied vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Divide(CitizenFX.Core.Vector3@,System.Single,CitizenFX.Core.Vector3@)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="result">When the method completes, contains the scaled vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Divide(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Divide(System.Single,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="value">The vector to scale.</param>
            <param name="result">When the method completes, contains the scaled vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Divide(System.Single,CitizenFX.Core.Vector3)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Negate(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Reverses the direction of a given vector.
            </summary>
            <param name="value">The vector to negate.</param>
            <param name="result">When the method completes, contains a vector facing in the opposite direction.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Negate(CitizenFX.Core.Vector3)">
            <summary>
            Reverses the direction of a given vector.
            </summary>
            <param name="value">The vector to negate.</param>
            <returns>A vector facing in the opposite direction.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Barycentric(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,System.Single,System.Single,CitizenFX.Core.Vector3@)">
            <summary>
            Returns a <see cref="T:CitizenFX.Core.Vector3"/> containing the 3D Cartesian coordinates of a point specified in Barycentric coordinates relative to a 3D triangle.
            </summary>
            <param name="value1">A <see cref="T:CitizenFX.Core.Vector3"/> containing the 3D Cartesian coordinates of vertex 1 of the triangle.</param>
            <param name="value2">A <see cref="T:CitizenFX.Core.Vector3"/> containing the 3D Cartesian coordinates of vertex 2 of the triangle.</param>
            <param name="value3">A <see cref="T:CitizenFX.Core.Vector3"/> containing the 3D Cartesian coordinates of vertex 3 of the triangle.</param>
            <param name="amount1">Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in <paramref name="value2"/>).</param>
            <param name="amount2">Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in <paramref name="value3"/>).</param>
            <param name="result">When the method completes, contains the 3D Cartesian coordinates of the specified point.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Barycentric(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,System.Single,System.Single)">
            <summary>
            Returns a <see cref="T:CitizenFX.Core.Vector3"/> containing the 3D Cartesian coordinates of a point specified in Barycentric coordinates relative to a 3D triangle.
            </summary>
            <param name="value1">A <see cref="T:CitizenFX.Core.Vector3"/> containing the 3D Cartesian coordinates of vertex 1 of the triangle.</param>
            <param name="value2">A <see cref="T:CitizenFX.Core.Vector3"/> containing the 3D Cartesian coordinates of vertex 2 of the triangle.</param>
            <param name="value3">A <see cref="T:CitizenFX.Core.Vector3"/> containing the 3D Cartesian coordinates of vertex 3 of the triangle.</param>
            <param name="amount1">Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in <paramref name="value2"/>).</param>
            <param name="amount2">Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in <paramref name="value3"/>).</param>
            <returns>A new <see cref="T:CitizenFX.Core.Vector3"/> containing the 3D Cartesian coordinates of the specified point.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Clamp(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Restricts a value to be within a specified range.
            </summary>
            <param name="value">The value to clamp.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="result">When the method completes, contains the clamped value.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Clamp(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Restricts a value to be within a specified range.
            </summary>
            <param name="value">The value to clamp.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <returns>The clamped value.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Cross(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Calculates the cross product of two vectors.
            </summary>
            <param name="left">First source vector.</param>
            <param name="right">Second source vector.</param>
            <param name="result">When the method completes, contains he cross product of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Cross(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Calculates the cross product of two vectors.
            </summary>
            <param name="left">First source vector.</param>
            <param name="right">Second source vector.</param>
            <returns>The cross product of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Distance(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,System.Single@)">
            <summary>
            Calculates the distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <param name="result">When the method completes, contains the distance between the two vectors.</param>
            <remarks>
            <see cref="M:CitizenFX.Core.Vector3.DistanceSquared(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,System.Single@)"/> may be preferred when only the relative distance is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Distance(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Calculates the distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <returns>The distance between the two vectors.</returns>
            <remarks>
            <see cref="M:CitizenFX.Core.Vector3.DistanceSquared(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)"/> may be preferred when only the relative distance is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.DistanceSquared(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,System.Single@)">
            <summary>
            Calculates the squared distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <param name="result">When the method completes, contains the squared distance between the two vectors.</param>
            <remarks>Distance squared is the value before taking the square root. 
            Distance squared can often be used in place of distance if relative comparisons are being made. 
            For example, consider three points A, B, and C. To determine whether B or C is further from A, 
            compare the distance between A and B to the distance between A and C. Calculating the two distances 
            involves two square roots, which are computationally expensive. However, using distance squared 
            provides the same information and avoids calculating two square roots.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.DistanceSquared(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Calculates the squared distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <returns>The squared distance between the two vectors.</returns>
            <remarks>Distance squared is the value before taking the square root. 
            Distance squared can often be used in place of distance if relative comparisons are being made. 
            For example, consider three points A, B, and C. To determine whether B or C is further from A, 
            compare the distance between A and B to the distance between A and C. Calculating the two distances 
            involves two square roots, which are computationally expensive. However, using distance squared 
            provides the same information and avoids calculating two square roots.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.NearEqual(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Tests whether one 3D vector is near another 3D vector.
            </summary>
            <param name="left">The left vector.</param>
            <param name="right">The right vector.</param>
            <param name="epsilon">The epsilon.</param>
            <returns><c>true</c> if left and right are near another 3D, <c>false</c> otherwise</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.NearEqual(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Tests whether one 3D vector is near another 3D vector.
            </summary>
            <param name="left">The left vector.</param>
            <param name="right">The right vector.</param>
            <param name="epsilon">The epsilon.</param>
            <returns><c>true</c> if left and right are near another 3D, <c>false</c> otherwise</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Dot(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,System.Single@)">
            <summary>
            Calculates the dot product of two vectors.
            </summary>
            <param name="left">First source vector.</param>
            <param name="right">Second source vector.</param>
            <param name="result">When the method completes, contains the dot product of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Dot(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Calculates the dot product of two vectors.
            </summary>
            <param name="left">First source vector.</param>
            <param name="right">Second source vector.</param>
            <returns>The dot product of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Normalize(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Converts the vector into a unit vector.
            </summary>
            <param name="value">The vector to normalize.</param>
            <param name="result">When the method completes, contains the normalized vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Normalize(CitizenFX.Core.Vector3)">
            <summary>
            Converts the vector into a unit vector.
            </summary>
            <param name="value">The vector to normalize.</param>
            <returns>The normalized vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Lerp(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,System.Single,CitizenFX.Core.Vector3@)">
            <summary>
            Performs a linear interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the linear interpolation of the two vectors.</param>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Lerp(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Performs a linear interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The linear interpolation of the two vectors.</returns>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.SmoothStep(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,System.Single,CitizenFX.Core.Vector3@)">
            <summary>
            Performs a cubic interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the cubic interpolation of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.SmoothStep(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Performs a cubic interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The cubic interpolation of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Hermite(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,System.Single,CitizenFX.Core.Vector3@)">
            <summary>
            Performs a Hermite spline interpolation.
            </summary>
            <param name="value1">First source position vector.</param>
            <param name="tangent1">First source tangent vector.</param>
            <param name="value2">Second source position vector.</param>
            <param name="tangent2">Second source tangent vector.</param>
            <param name="amount">Weighting factor.</param>
            <param name="result">When the method completes, contains the result of the Hermite spline interpolation.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Hermite(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Performs a Hermite spline interpolation.
            </summary>
            <param name="value1">First source position vector.</param>
            <param name="tangent1">First source tangent vector.</param>
            <param name="value2">Second source position vector.</param>
            <param name="tangent2">Second source tangent vector.</param>
            <param name="amount">Weighting factor.</param>
            <returns>The result of the Hermite spline interpolation.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.CatmullRom(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,System.Single,CitizenFX.Core.Vector3@)">
            <summary>
            Performs a Catmull-Rom interpolation using the specified positions.
            </summary>
            <param name="value1">The first position in the interpolation.</param>
            <param name="value2">The second position in the interpolation.</param>
            <param name="value3">The third position in the interpolation.</param>
            <param name="value4">The fourth position in the interpolation.</param>
            <param name="amount">Weighting factor.</param>
            <param name="result">When the method completes, contains the result of the Catmull-Rom interpolation.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.CatmullRom(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Performs a Catmull-Rom interpolation using the specified positions.
            </summary>
            <param name="value1">The first position in the interpolation.</param>
            <param name="value2">The second position in the interpolation.</param>
            <param name="value3">The third position in the interpolation.</param>
            <param name="value4">The fourth position in the interpolation.</param>
            <param name="amount">Weighting factor.</param>
            <returns>A vector that is the result of the Catmull-Rom interpolation.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Max(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Returns a vector containing the largest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <param name="result">When the method completes, contains an new vector composed of the largest components of the source vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Max(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Returns a vector containing the largest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <returns>A vector containing the largest components of the source vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Min(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Returns a vector containing the smallest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <param name="result">When the method completes, contains an new vector composed of the smallest components of the source vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Min(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Returns a vector containing the smallest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <returns>A vector containing the smallest components of the source vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Project(CitizenFX.Core.Vector3@,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector3@)">
            <summary>
            Projects a 3D vector from object space into screen space. 
            </summary>
            <param name="vector">The vector to project.</param>
            <param name="x">The X position of the viewport.</param>
            <param name="y">The Y position of the viewport.</param>
            <param name="width">The width of the viewport.</param>
            <param name="height">The height of the viewport.</param>
            <param name="minZ">The minimum depth of the viewport.</param>
            <param name="maxZ">The maximum depth of the viewport.</param>
            <param name="worldViewProjection">The combined world-view-projection matrix.</param>
            <param name="result">When the method completes, contains the vector in screen space.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Project(CitizenFX.Core.Vector3,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix)">
            <summary>
            Projects a 3D vector from object space into screen space. 
            </summary>
            <param name="vector">The vector to project.</param>
            <param name="x">The X position of the viewport.</param>
            <param name="y">The Y position of the viewport.</param>
            <param name="width">The width of the viewport.</param>
            <param name="height">The height of the viewport.</param>
            <param name="minZ">The minimum depth of the viewport.</param>
            <param name="maxZ">The maximum depth of the viewport.</param>
            <param name="worldViewProjection">The combined world-view-projection matrix.</param>
            <returns>The vector in screen space.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Unproject(CitizenFX.Core.Vector3@,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector3@)">
            <summary>
            Projects a 3D vector from screen space into object space. 
            </summary>
            <param name="vector">The vector to project.</param>
            <param name="x">The X position of the viewport.</param>
            <param name="y">The Y position of the viewport.</param>
            <param name="width">The width of the viewport.</param>
            <param name="height">The height of the viewport.</param>
            <param name="minZ">The minimum depth of the viewport.</param>
            <param name="maxZ">The maximum depth of the viewport.</param>
            <param name="worldViewProjection">The combined world-view-projection matrix.</param>
            <param name="result">When the method completes, contains the vector in object space.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Unproject(CitizenFX.Core.Vector3,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,CitizenFX.Core.Matrix)">
            <summary>
            Projects a 3D vector from screen space into object space. 
            </summary>
            <param name="vector">The vector to project.</param>
            <param name="x">The X position of the viewport.</param>
            <param name="y">The Y position of the viewport.</param>
            <param name="width">The width of the viewport.</param>
            <param name="height">The height of the viewport.</param>
            <param name="minZ">The minimum depth of the viewport.</param>
            <param name="maxZ">The maximum depth of the viewport.</param>
            <param name="worldViewProjection">The combined world-view-projection matrix.</param>
            <returns>The vector in object space.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Reflect(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)">
            <summary>
            Returns the reflection of a vector off a surface that has the specified normal. 
            </summary>
            <param name="vector">The source vector.</param>
            <param name="normal">Normal of the surface.</param>
            <param name="result">When the method completes, contains the reflected vector.</param>
            <remarks>Reflect only gives the direction of a reflection off a surface, it does not determine 
            whether the original vector was close enough to the surface to hit it.</remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Reflect(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Returns the reflection of a vector off a surface that has the specified normal. 
            </summary>
            <param name="vector">The source vector.</param>
            <param name="normal">Normal of the surface.</param>
            <returns>The reflected vector.</returns>
            <remarks>Reflect only gives the direction of a reflection off a surface, it does not determine 
            whether the original vector was close enough to the surface to hit it.</remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Orthogonalize(CitizenFX.Core.Vector3[],CitizenFX.Core.Vector3[])">
            <summary>
            Orthogonalizes a list of vectors.
            </summary>
            <param name="destination">The list of orthogonalized vectors.</param>
            <param name="source">The list of vectors to orthogonalize.</param>
            <remarks>
            <para>Orthogonalization is the process of making all vectors orthogonal to each other. This
            means that any given vector in the list will be orthogonal to any other given vector in the
            list.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting vectors
            tend to be numerically unstable. The numeric stability decreases according to the vectors
            position in the list so that the first vector is the most stable and the last vector is the
            least stable.</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Orthonormalize(CitizenFX.Core.Vector3[],CitizenFX.Core.Vector3[])">
            <summary>
            Orthonormalizes a list of vectors.
            </summary>
            <param name="destination">The list of orthonormalized vectors.</param>
            <param name="source">The list of vectors to orthonormalize.</param>
            <remarks>
            <para>Orthonormalization is the process of making all vectors orthogonal to each
            other and making all vectors of unit length. This means that any given vector will
            be orthogonal to any other given vector in the list.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting vectors
            tend to be numerically unstable. The numeric stability decreases according to the vectors
            position in the list so that the first vector is the most stable and the last vector is the
            least stable.</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Transform(CitizenFX.Core.Vector3@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector3@)">
            <summary>
            Transforms a 3D vector by the given <see cref="T:CitizenFX.Core.Quaternion"/> rotation.
            </summary>
            <param name="vector">The vector to rotate.</param>
            <param name="rotation">The <see cref="T:CitizenFX.Core.Quaternion"/> rotation to apply.</param>
            <param name="result">When the method completes, contains the transformed <see cref="T:CitizenFX.Core.Vector4"/>.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Transform(CitizenFX.Core.Vector3,CitizenFX.Core.Quaternion)">
            <summary>
            Transforms a 3D vector by the given <see cref="T:CitizenFX.Core.Quaternion"/> rotation.
            </summary>
            <param name="vector">The vector to rotate.</param>
            <param name="rotation">The <see cref="T:CitizenFX.Core.Quaternion"/> rotation to apply.</param>
            <returns>The transformed <see cref="T:CitizenFX.Core.Vector4"/>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Transform(CitizenFX.Core.Vector3[],CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector3[])">
            <summary>
            Transforms an array of vectors by the given <see cref="T:CitizenFX.Core.Quaternion"/> rotation.
            </summary>
            <param name="source">The array of vectors to transform.</param>
            <param name="rotation">The <see cref="T:CitizenFX.Core.Quaternion"/> rotation to apply.</param>
            <param name="destination">The array for which the transformed vectors are stored.
            This array may be the same array as <paramref name="source"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Transform(CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix3x3@,CitizenFX.Core.Vector3@)">
            <summary>
            Transforms a 3D vector by the given <see cref="T:CitizenFX.Core.Matrix3x3"/>.
            </summary>
            <param name="vector">The source vector.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix3x3"/>.</param>
            <param name="result">When the method completes, contains the transformed <see cref="T:CitizenFX.Core.Vector3"/>.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Transform(CitizenFX.Core.Vector3,CitizenFX.Core.Matrix3x3)">
            <summary>
            Transforms a 3D vector by the given <see cref="T:CitizenFX.Core.Matrix3x3"/>.
            </summary>
            <param name="vector">The source vector.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix3x3"/>.</param>
            <returns>The transformed <see cref="T:CitizenFX.Core.Vector3"/>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Transform(CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector3@)">
            <summary>
            Transforms a 3D vector by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="vector">The source vector.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="result">When the method completes, contains the transformed <see cref="T:CitizenFX.Core.Vector3"/>.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Transform(CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector4@)">
            <summary>
            Transforms a 3D vector by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="vector">The source vector.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="result">When the method completes, contains the transformed <see cref="T:CitizenFX.Core.Vector4"/>.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Transform(CitizenFX.Core.Vector3,CitizenFX.Core.Matrix)">
            <summary>
            Transforms a 3D vector by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="vector">The source vector.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <returns>The transformed <see cref="T:CitizenFX.Core.Vector4"/>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Transform(CitizenFX.Core.Vector3[],CitizenFX.Core.Matrix@,CitizenFX.Core.Vector4[])">
            <summary>
            Transforms an array of 3D vectors by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="source">The array of vectors to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="destination">The array for which the transformed vectors are stored.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector3.TransformCoordinate(CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector3@)">
            <summary>
            Performs a coordinate transformation using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="coordinate">The coordinate vector to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="result">When the method completes, contains the transformed coordinates.</param>
            <remarks>
            A coordinate transform performs the transformation with the assumption that the w component
            is one. The four dimensional vector obtained from the transformation operation has each
            component in the vector divided by the w component. This forces the w component to be one and
            therefore makes the vector homogeneous. The homogeneous vector is often preferred when working
            with coordinates as the w component can safely be ignored.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.TransformCoordinate(CitizenFX.Core.Vector3,CitizenFX.Core.Matrix)">
            <summary>
            Performs a coordinate transformation using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="coordinate">The coordinate vector to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <returns>The transformed coordinates.</returns>
            <remarks>
            A coordinate transform performs the transformation with the assumption that the w component
            is one. The four dimensional vector obtained from the transformation operation has each
            component in the vector divided by the w component. This forces the w component to be one and
            therefore makes the vector homogeneous. The homogeneous vector is often preferred when working
            with coordinates as the w component can safely be ignored.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.TransformCoordinate(CitizenFX.Core.Vector3[],CitizenFX.Core.Matrix@,CitizenFX.Core.Vector3[])">
            <summary>
            Performs a coordinate transformation on an array of vectors using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="source">The array of coordinate vectors to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="destination">The array for which the transformed vectors are stored.
            This array may be the same array as <paramref name="source"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
            <remarks>
            A coordinate transform performs the transformation with the assumption that the w component
            is one. The four dimensional vector obtained from the transformation operation has each
            component in the vector divided by the w component. This forces the w component to be one and
            therefore makes the vector homogeneous. The homogeneous vector is often preferred when working
            with coordinates as the w component can safely be ignored.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.TransformNormal(CitizenFX.Core.Vector3@,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector3@)">
            <summary>
            Performs a normal transformation using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="normal">The normal vector to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="result">When the method completes, contains the transformed normal.</param>
            <remarks>
            A normal transform performs the transformation with the assumption that the w component
            is zero. This causes the fourth row and fourth column of the matrix to be unused. The
            end result is a vector that is not translated, but all other transformation properties
            apply. This is often preferred for normal vectors as normals purely represent direction
            rather than location because normal vectors should not be translated.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.TransformNormal(CitizenFX.Core.Vector3,CitizenFX.Core.Matrix)">
            <summary>
            Performs a normal transformation using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="normal">The normal vector to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <returns>The transformed normal.</returns>
            <remarks>
            A normal transform performs the transformation with the assumption that the w component
            is zero. This causes the fourth row and fourth column of the matrix to be unused. The
            end result is a vector that is not translated, but all other transformation properties
            apply. This is often preferred for normal vectors as normals purely represent direction
            rather than location because normal vectors should not be translated.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.TransformNormal(CitizenFX.Core.Vector3[],CitizenFX.Core.Matrix@,CitizenFX.Core.Vector3[])">
            <summary>
            Performs a normal transformation on an array of vectors using the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="source">The array of normal vectors to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="destination">The array for which the transformed vectors are stored.
            This array may be the same array as <paramref name="source"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
            <remarks>
            A normal transform performs the transformation with the assumption that the w component
            is zero. This causes the fourth row and fourth column of the matrix to be unused. The
            end result is a vector that is not translated, but all other transformation properties
            apply. This is often preferred for normal vectors as normals purely represent direction
            rather than location because normal vectors should not be translated.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Addition(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Adds two vectors.
            </summary>
            <param name="left">The first vector to add.</param>
            <param name="right">The second vector to add.</param>
            <returns>The sum of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Multiply(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Multiplies a vector with another by performing component-wise multiplication equivalent to <see cref="M:CitizenFX.Core.Vector3.Multiply(CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@,CitizenFX.Core.Vector3@)"/>.
            </summary>
            <param name="left">The first vector to multiply.</param>
            <param name="right">The second vector to multiply.</param>
            <returns>The multiplication of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_UnaryPlus(CitizenFX.Core.Vector3)">
            <summary>
            Assert a vector (return it unchanged).
            </summary>
            <param name="value">The vector to assert (unchanged).</param>
            <returns>The asserted (unchanged) vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Subtraction(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Subtracts two vectors.
            </summary>
            <param name="left">The first vector to subtract.</param>
            <param name="right">The second vector to subtract.</param>
            <returns>The difference of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_UnaryNegation(CitizenFX.Core.Vector3)">
            <summary>
            Reverses the direction of a given vector.
            </summary>
            <param name="value">The vector to negate.</param>
            <returns>A vector facing in the opposite direction.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Multiply(System.Single,CitizenFX.Core.Vector3)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Multiply(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Division(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Division(System.Single,CitizenFX.Core.Vector3)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="value">The vector to scale.</param>  
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Division(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Addition(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be added on elements</param>
            <returns>The vector with added scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Addition(System.Single,CitizenFX.Core.Vector3)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be added on elements</param>
            <returns>The vector with added scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Subtraction(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be subtraced from elements</param>
            <returns>The vector with added scalar from each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Subtraction(System.Single,CitizenFX.Core.Vector3)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be subtraced from elements</param>
            <returns>The vector with subtraced scalar from each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Equality(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Tests for equality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has the same value as <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Inequality(CitizenFX.Core.Vector3,CitizenFX.Core.Vector3)">
            <summary>
            Tests for inequality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has a different value than <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Explicit(CitizenFX.Core.Vector3)~CitizenFX.Core.Vector2">
            <summary>
            Performs an explicit conversion from <see cref="T:CitizenFX.Core.Vector3"/> to <see cref="T:CitizenFX.Core.Vector2"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.op_Explicit(CitizenFX.Core.Vector3)~CitizenFX.Core.Vector4">
            <summary>
            Performs an explicit conversion from <see cref="T:CitizenFX.Core.Vector3"/> to <see cref="T:CitizenFX.Core.Vector4"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.ToString(System.String)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.ToString(System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Equals(CitizenFX.Core.Vector3@)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Vector3"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Vector3"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:CitizenFX.Core.Vector3"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Equals(CitizenFX.Core.Vector3)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Vector3"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Vector3"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:CitizenFX.Core.Vector3"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector3.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            	<c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:CitizenFX.Core.Vector4">
            <summary>
            Represents a four dimensional mathematical vector.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.Zero">
            <summary>
            A <see cref="T:CitizenFX.Core.Vector4"/> with all of its components set to zero.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.UnitX">
            <summary>
            The X unit <see cref="T:CitizenFX.Core.Vector4"/> (1, 0, 0, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.UnitY">
            <summary>
            The Y unit <see cref="T:CitizenFX.Core.Vector4"/> (0, 1, 0, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.UnitZ">
            <summary>
            The Z unit <see cref="T:CitizenFX.Core.Vector4"/> (0, 0, 1, 0).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.UnitW">
            <summary>
            The W unit <see cref="T:CitizenFX.Core.Vector4"/> (0, 0, 0, 1).
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.One">
            <summary>
            A <see cref="T:CitizenFX.Core.Vector4"/> with all of its components set to one.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.X">
            <summary>
            The X component of the vector.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.Y">
            <summary>
            The Y component of the vector.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.Z">
            <summary>
            The Z component of the vector.
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Vector4.W">
            <summary>
            The W component of the vector.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Vector4.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector4"/> struct.
            </summary>
            <param name="value">The value that will be assigned to all components.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector4"/> struct.
            </summary>
            <param name="x">Initial value for the X component of the vector.</param>
            <param name="y">Initial value for the Y component of the vector.</param>
            <param name="z">Initial value for the Z component of the vector.</param>
            <param name="w">Initial value for the W component of the vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.#ctor(CitizenFX.Core.Vector3,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector4"/> struct.
            </summary>
            <param name="value">A vector containing the values with which to initialize the X, Y, and Z components.</param>
            <param name="w">Initial value for the W component of the vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.#ctor(CitizenFX.Core.Vector2,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector4"/> struct.
            </summary>
            <param name="value">A vector containing the values with which to initialize the X and Y components.</param>
            <param name="z">Initial value for the Z component of the vector.</param>
            <param name="w">Initial value for the W component of the vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.#ctor(System.Single[])">
            <summary>
            Initializes a new instance of the <see cref="T:CitizenFX.Core.Vector4"/> struct.
            </summary>
            <param name="values">The values to assign to the X, Y, Z, and W components of the vector. This must be an array with four elements.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="values"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="values"/> contains more or less than four elements.</exception>
        </member>
        <member name="P:CitizenFX.Core.Vector4.IsNormalized">
            <summary>
            Gets a value indicting whether this instance is normalized.
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Vector4.IsZero">
            <summary>
            Gets a value indicting whether this vector is zero
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Vector4.Item(System.Int32)">
            <summary>
            Gets or sets the component at the specified index.
            </summary>
            <value>The value of the X, Y, Z, or W component, depending on the index.</value>
            <param name="index">The index of the component to access. Use 0 for the X component, 1 for the Y component, 2 for the Z component, and 3 for the W component.</param>
            <returns>The value of the component at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the <paramref name="index"/> is out of the range [0, 3].</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Length">
            <summary>
            Calculates the length of the vector.
            </summary>
            <returns>The length of the vector.</returns>
            <remarks>
            <see cref="M:CitizenFX.Core.Vector4.LengthSquared"/> may be preferred when only the relative length is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector4.LengthSquared">
            <summary>
            Calculates the squared length of the vector.
            </summary>
            <returns>The squared length of the vector.</returns>
            <remarks>
            This method may be preferred to <see cref="M:CitizenFX.Core.Vector4.Length"/> when only a relative length is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Normalize">
            <summary>
            Converts the vector into a unit vector.
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Vector4.ToArray">
            <summary>
            Creates an array containing the elements of the vector.
            </summary>
            <returns>A four-element array containing the components of the vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Add(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Adds two vectors.
            </summary>
            <param name="left">The first vector to add.</param>
            <param name="right">The second vector to add.</param>
            <param name="result">When the method completes, contains the sum of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Add(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Adds two vectors.
            </summary>
            <param name="left">The first vector to add.</param>
            <param name="right">The second vector to add.</param>
            <returns>The sum of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Add(CitizenFX.Core.Vector4@,System.Single@,CitizenFX.Core.Vector4@)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be added to elements</param>
            <param name="result">The vector with added scalar for each element.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Add(CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be added to elements</param>
            <returns>The vector with added scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Subtract(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Subtracts two vectors.
            </summary>
            <param name="left">The first vector to subtract.</param>
            <param name="right">The second vector to subtract.</param>
            <param name="result">When the method completes, contains the difference of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Subtract(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Subtracts two vectors.
            </summary>
            <param name="left">The first vector to subtract.</param>
            <param name="right">The second vector to subtract.</param>
            <returns>The difference of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Subtract(CitizenFX.Core.Vector4@,System.Single@,CitizenFX.Core.Vector4@)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be subtraced from elements</param>
            <param name="result">The vector with subtracted scalar for each element.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Subtract(CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The input vector</param>
            <param name="right">The scalar value to be subtraced from elements</param>
            <returns>The vector with subtracted scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Subtract(System.Single@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The scalar value to be subtraced from elements</param>
            <param name="right">The input vector.</param>
            <param name="result">The vector with subtracted scalar for each element.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Subtract(System.Single,CitizenFX.Core.Vector4)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="left">The scalar value to be subtraced from elements</param>
            <param name="right">The input vector.</param>
            <returns>The vector with subtracted scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Multiply(CitizenFX.Core.Vector4@,System.Single,CitizenFX.Core.Vector4@)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="result">When the method completes, contains the scaled vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Multiply(CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Multiply(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Multiplies a vector with another by performing component-wise multiplication.
            </summary>
            <param name="left">The first vector to multiply.</param>
            <param name="right">The second vector to multiply.</param>
            <param name="result">When the method completes, contains the multiplied vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Multiply(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Multiplies a vector with another by performing component-wise multiplication.
            </summary>
            <param name="left">The first vector to multiply.</param>
            <param name="right">The second vector to multiply.</param>
            <returns>The multiplied vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Divide(CitizenFX.Core.Vector4@,System.Single,CitizenFX.Core.Vector4@)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="result">When the method completes, contains the scaled vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Divide(CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Divide(System.Single,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="value">The vector to scale.</param>
            <param name="result">When the method completes, contains the scaled vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Divide(System.Single,CitizenFX.Core.Vector4)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Negate(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Reverses the direction of a given vector.
            </summary>
            <param name="value">The vector to negate.</param>
            <param name="result">When the method completes, contains a vector facing in the opposite direction.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Negate(CitizenFX.Core.Vector4)">
            <summary>
            Reverses the direction of a given vector.
            </summary>
            <param name="value">The vector to negate.</param>
            <returns>A vector facing in the opposite direction.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Barycentric(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,System.Single,System.Single,CitizenFX.Core.Vector4@)">
            <summary>
            Returns a <see cref="T:CitizenFX.Core.Vector4"/> containing the 4D Cartesian coordinates of a point specified in Barycentric coordinates relative to a 4D triangle.
            </summary>
            <param name="value1">A <see cref="T:CitizenFX.Core.Vector4"/> containing the 4D Cartesian coordinates of vertex 1 of the triangle.</param>
            <param name="value2">A <see cref="T:CitizenFX.Core.Vector4"/> containing the 4D Cartesian coordinates of vertex 2 of the triangle.</param>
            <param name="value3">A <see cref="T:CitizenFX.Core.Vector4"/> containing the 4D Cartesian coordinates of vertex 3 of the triangle.</param>
            <param name="amount1">Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in <paramref name="value2"/>).</param>
            <param name="amount2">Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in <paramref name="value3"/>).</param>
            <param name="result">When the method completes, contains the 4D Cartesian coordinates of the specified point.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Barycentric(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,System.Single,System.Single)">
            <summary>
            Returns a <see cref="T:CitizenFX.Core.Vector4"/> containing the 4D Cartesian coordinates of a point specified in Barycentric coordinates relative to a 4D triangle.
            </summary>
            <param name="value1">A <see cref="T:CitizenFX.Core.Vector4"/> containing the 4D Cartesian coordinates of vertex 1 of the triangle.</param>
            <param name="value2">A <see cref="T:CitizenFX.Core.Vector4"/> containing the 4D Cartesian coordinates of vertex 2 of the triangle.</param>
            <param name="value3">A <see cref="T:CitizenFX.Core.Vector4"/> containing the 4D Cartesian coordinates of vertex 3 of the triangle.</param>
            <param name="amount1">Barycentric coordinate b2, which expresses the weighting factor toward vertex 2 (specified in <paramref name="value2"/>).</param>
            <param name="amount2">Barycentric coordinate b3, which expresses the weighting factor toward vertex 3 (specified in <paramref name="value3"/>).</param>
            <returns>A new <see cref="T:CitizenFX.Core.Vector4"/> containing the 4D Cartesian coordinates of the specified point.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Clamp(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Restricts a value to be within a specified range.
            </summary>
            <param name="value">The value to clamp.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="result">When the method completes, contains the clamped value.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Clamp(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Restricts a value to be within a specified range.
            </summary>
            <param name="value">The value to clamp.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <returns>The clamped value.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Distance(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,System.Single@)">
            <summary>
            Calculates the distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <param name="result">When the method completes, contains the distance between the two vectors.</param>
            <remarks>
            <see cref="M:CitizenFX.Core.Vector4.DistanceSquared(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,System.Single@)"/> may be preferred when only the relative distance is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Distance(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Calculates the distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <returns>The distance between the two vectors.</returns>
            <remarks>
            <see cref="M:CitizenFX.Core.Vector4.DistanceSquared(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)"/> may be preferred when only the relative distance is needed
            and speed is of the essence.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector4.DistanceSquared(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,System.Single@)">
            <summary>
            Calculates the squared distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <param name="result">When the method completes, contains the squared distance between the two vectors.</param>
            <remarks>Distance squared is the value before taking the square root. 
            Distance squared can often be used in place of distance if relative comparisons are being made. 
            For example, consider three points A, B, and C. To determine whether B or C is further from A, 
            compare the distance between A and B to the distance between A and C. Calculating the two distances 
            involves two square roots, which are computationally expensive. However, using distance squared 
            provides the same information and avoids calculating two square roots.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector4.DistanceSquared(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Calculates the squared distance between two vectors.
            </summary>
            <param name="value1">The first vector.</param>
            <param name="value2">The second vector.</param>
            <returns>The squared distance between the two vectors.</returns>
            <remarks>Distance squared is the value before taking the square root. 
            Distance squared can often be used in place of distance if relative comparisons are being made. 
            For example, consider three points A, B, and C. To determine whether B or C is further from A, 
            compare the distance between A and B to the distance between A and C. Calculating the two distances 
            involves two square roots, which are computationally expensive. However, using distance squared 
            provides the same information and avoids calculating two square roots.
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Dot(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,System.Single@)">
            <summary>
            Calculates the dot product of two vectors.
            </summary>
            <param name="left">First source vector</param>
            <param name="right">Second source vector.</param>
            <param name="result">When the method completes, contains the dot product of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Dot(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Calculates the dot product of two vectors.
            </summary>
            <param name="left">First source vector.</param>
            <param name="right">Second source vector.</param>
            <returns>The dot product of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Normalize(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Converts the vector into a unit vector.
            </summary>
            <param name="value">The vector to normalize.</param>
            <param name="result">When the method completes, contains the normalized vector.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Normalize(CitizenFX.Core.Vector4)">
            <summary>
            Converts the vector into a unit vector.
            </summary>
            <param name="value">The vector to normalize.</param>
            <returns>The normalized vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Lerp(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,System.Single,CitizenFX.Core.Vector4@)">
            <summary>
            Performs a linear interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the linear interpolation of the two vectors.</param>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Lerp(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Performs a linear interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The linear interpolation of the two vectors.</returns>
            <remarks>
            Passing <paramref name="amount"/> a value of 0 will cause <paramref name="start"/> to be returned; a value of 1 will cause <paramref name="end"/> to be returned. 
            </remarks>
        </member>
        <member name="M:CitizenFX.Core.Vector4.SmoothStep(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,System.Single,CitizenFX.Core.Vector4@)">
            <summary>
            Performs a cubic interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <param name="result">When the method completes, contains the cubic interpolation of the two vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.SmoothStep(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Performs a cubic interpolation between two vectors.
            </summary>
            <param name="start">Start vector.</param>
            <param name="end">End vector.</param>
            <param name="amount">Value between 0 and 1 indicating the weight of <paramref name="end"/>.</param>
            <returns>The cubic interpolation of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Hermite(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,System.Single,CitizenFX.Core.Vector4@)">
            <summary>
            Performs a Hermite spline interpolation.
            </summary>
            <param name="value1">First source position vector.</param>
            <param name="tangent1">First source tangent vector.</param>
            <param name="value2">Second source position vector.</param>
            <param name="tangent2">Second source tangent vector.</param>
            <param name="amount">Weighting factor.</param>
            <param name="result">When the method completes, contains the result of the Hermite spline interpolation.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Hermite(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Performs a Hermite spline interpolation.
            </summary>
            <param name="value1">First source position vector.</param>
            <param name="tangent1">First source tangent vector.</param>
            <param name="value2">Second source position vector.</param>
            <param name="tangent2">Second source tangent vector.</param>
            <param name="amount">Weighting factor.</param>
            <returns>The result of the Hermite spline interpolation.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.CatmullRom(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,System.Single,CitizenFX.Core.Vector4@)">
            <summary>
            Performs a Catmull-Rom interpolation using the specified positions.
            </summary>
            <param name="value1">The first position in the interpolation.</param>
            <param name="value2">The second position in the interpolation.</param>
            <param name="value3">The third position in the interpolation.</param>
            <param name="value4">The fourth position in the interpolation.</param>
            <param name="amount">Weighting factor.</param>
            <param name="result">When the method completes, contains the result of the Catmull-Rom interpolation.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.CatmullRom(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Performs a Catmull-Rom interpolation using the specified positions.
            </summary>
            <param name="value1">The first position in the interpolation.</param>
            <param name="value2">The second position in the interpolation.</param>
            <param name="value3">The third position in the interpolation.</param>
            <param name="value4">The fourth position in the interpolation.</param>
            <param name="amount">Weighting factor.</param>
            <returns>A vector that is the result of the Catmull-Rom interpolation.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Max(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Returns a vector containing the largest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <param name="result">When the method completes, contains an new vector composed of the largest components of the source vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Max(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Returns a vector containing the largest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <returns>A vector containing the largest components of the source vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Min(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)">
            <summary>
            Returns a vector containing the smallest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <param name="result">When the method completes, contains an new vector composed of the smallest components of the source vectors.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Min(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Returns a vector containing the smallest components of the specified vectors.
            </summary>
            <param name="left">The first source vector.</param>
            <param name="right">The second source vector.</param>
            <returns>A vector containing the smallest components of the source vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Orthogonalize(CitizenFX.Core.Vector4[],CitizenFX.Core.Vector4[])">
            <summary>
            Orthogonalizes a list of vectors.
            </summary>
            <param name="destination">The list of orthogonalized vectors.</param>
            <param name="source">The list of vectors to orthogonalize.</param>
            <remarks>
            <para>Orthogonalization is the process of making all vectors orthogonal to each other. This
            means that any given vector in the list will be orthogonal to any other given vector in the
            list.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting vectors
            tend to be numerically unstable. The numeric stability decreases according to the vectors
            position in the list so that the first vector is the most stable and the last vector is the
            least stable.</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Orthonormalize(CitizenFX.Core.Vector4[],CitizenFX.Core.Vector4[])">
            <summary>
            Orthonormalizes a list of vectors.
            </summary>
            <param name="destination">The list of orthonormalized vectors.</param>
            <param name="source">The list of vectors to orthonormalize.</param>
            <remarks>
            <para>Orthonormalization is the process of making all vectors orthogonal to each
            other and making all vectors of unit length. This means that any given vector will
            be orthogonal to any other given vector in the list.</para>
            <para>Because this method uses the modified Gram-Schmidt process, the resulting vectors
            tend to be numerically unstable. The numeric stability decreases according to the vectors
            position in the list so that the first vector is the most stable and the last vector is the
            least stable.</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Transform(CitizenFX.Core.Vector4@,CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector4@)">
            <summary>
            Transforms a 4D vector by the given <see cref="T:CitizenFX.Core.Quaternion"/> rotation.
            </summary>
            <param name="vector">The vector to rotate.</param>
            <param name="rotation">The <see cref="T:CitizenFX.Core.Quaternion"/> rotation to apply.</param>
            <param name="result">When the method completes, contains the transformed <see cref="T:CitizenFX.Core.Vector4"/>.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Transform(CitizenFX.Core.Vector4,CitizenFX.Core.Quaternion)">
            <summary>
            Transforms a 4D vector by the given <see cref="T:CitizenFX.Core.Quaternion"/> rotation.
            </summary>
            <param name="vector">The vector to rotate.</param>
            <param name="rotation">The <see cref="T:CitizenFX.Core.Quaternion"/> rotation to apply.</param>
            <returns>The transformed <see cref="T:CitizenFX.Core.Vector4"/>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Transform(CitizenFX.Core.Vector4[],CitizenFX.Core.Quaternion@,CitizenFX.Core.Vector4[])">
            <summary>
            Transforms an array of vectors by the given <see cref="T:CitizenFX.Core.Quaternion"/> rotation.
            </summary>
            <param name="source">The array of vectors to transform.</param>
            <param name="rotation">The <see cref="T:CitizenFX.Core.Quaternion"/> rotation to apply.</param>
            <param name="destination">The array for which the transformed vectors are stored.
            This array may be the same array as <paramref name="source"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Transform(CitizenFX.Core.Vector4@,CitizenFX.Core.Matrix@,CitizenFX.Core.Vector4@)">
            <summary>
            Transforms a 4D vector by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="vector">The source vector.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="result">When the method completes, contains the transformed <see cref="T:CitizenFX.Core.Vector4"/>.</param>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Transform(CitizenFX.Core.Vector4,CitizenFX.Core.Matrix)">
            <summary>
            Transforms a 4D vector by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="vector">The source vector.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <returns>The transformed <see cref="T:CitizenFX.Core.Vector4"/>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Transform(CitizenFX.Core.Vector4[],CitizenFX.Core.Matrix@,CitizenFX.Core.Vector4[])">
            <summary>
            Transforms an array of 4D vectors by the given <see cref="T:CitizenFX.Core.Matrix"/>.
            </summary>
            <param name="source">The array of vectors to transform.</param>
            <param name="transform">The transformation <see cref="T:CitizenFX.Core.Matrix"/>.</param>
            <param name="destination">The array for which the transformed vectors are stored.
            This array may be the same array as <paramref name="source"/>.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="source"/> or <paramref name="destination"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when <paramref name="destination"/> is shorter in length than <paramref name="source"/>.</exception>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Addition(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Adds two vectors.
            </summary>
            <param name="left">The first vector to add.</param>
            <param name="right">The second vector to add.</param>
            <returns>The sum of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Multiply(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Multiplies a vector with another by performing component-wise multiplication equivalent to <see cref="M:CitizenFX.Core.Vector4.Multiply(CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@,CitizenFX.Core.Vector4@)"/>.
            </summary>
            <param name="left">The first vector to multiply.</param>
            <param name="right">The second vector to multiply.</param>
            <returns>The multiplication of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_UnaryPlus(CitizenFX.Core.Vector4)">
            <summary>
            Assert a vector (return it unchanged).
            </summary>
            <param name="value">The vector to assert (unchanged).</param>
            <returns>The asserted (unchanged) vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Subtraction(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Subtracts two vectors.
            </summary>
            <param name="left">The first vector to subtract.</param>
            <param name="right">The second vector to subtract.</param>
            <returns>The difference of the two vectors.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_UnaryNegation(CitizenFX.Core.Vector4)">
            <summary>
            Reverses the direction of a given vector.
            </summary>
            <param name="value">The vector to negate.</param>
            <returns>A vector facing in the opposite direction.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Multiply(System.Single,CitizenFX.Core.Vector4)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Multiply(CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Division(CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Division(System.Single,CitizenFX.Core.Vector4)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="scale">The amount by which to scale the vector.</param>
            <param name="value">The vector to scale.</param>        
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Division(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Scales a vector by the given value.
            </summary>
            <param name="value">The vector to scale.</param>
            <param name="scale">The amount by which to scale the vector.</param>
            <returns>The scaled vector.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Addition(CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be added on elements</param>
            <returns>The vector with added scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Addition(System.Single,CitizenFX.Core.Vector4)">
            <summary>
            Perform a component-wise addition
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be added on elements</param>
            <returns>The vector with added scalar for each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Subtraction(CitizenFX.Core.Vector4,System.Single)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be subtraced from elements</param>
            <returns>The vector with subtraced scalar from each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Subtraction(System.Single,CitizenFX.Core.Vector4)">
            <summary>
            Perform a component-wise subtraction
            </summary>
            <param name="value">The input vector.</param>
            <param name="scalar">The scalar value to be subtraced from elements</param>
            <returns>The vector with subtraced scalar from each element.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Equality(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Tests for equality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has the same value as <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Inequality(CitizenFX.Core.Vector4,CitizenFX.Core.Vector4)">
            <summary>
            Tests for inequality between two objects.
            </summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns><c>true</c> if <paramref name="left"/> has a different value than <paramref name="right"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Explicit(CitizenFX.Core.Vector4)~CitizenFX.Core.Vector2">
            <summary>
            Performs an explicit conversion from <see cref="T:CitizenFX.Core.Vector4"/> to <see cref="T:CitizenFX.Core.Vector2"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.op_Explicit(CitizenFX.Core.Vector4)~CitizenFX.Core.Vector3">
            <summary>
            Performs an explicit conversion from <see cref="T:CitizenFX.Core.Vector4"/> to <see cref="T:CitizenFX.Core.Vector3"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.ToString(System.String)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.ToString(System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <param name="format">The format.</param>
            <param name="formatProvider">The format provider.</param>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Equals(CitizenFX.Core.Vector4@)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Vector4"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Vector4"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:CitizenFX.Core.Vector4"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Equals(CitizenFX.Core.Vector4)">
            <summary>
            Determines whether the specified <see cref="T:CitizenFX.Core.Vector4"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:CitizenFX.Core.Vector4"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:CitizenFX.Core.Vector4"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:CitizenFX.Core.Vector4.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
            <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="F:CitizenFX.Core.Color.B">
            <summary>
            Get the blue channel of this color
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Color.G">
            <summary>
            Get the green channel of this color
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Color.R">
            <summary>
            Get the red channel of this color
            </summary>
        </member>
        <member name="F:CitizenFX.Core.Color.A">
            <summary>
            Get the alpha/translucency channel of this color
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Color.#ctor(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Create a color by individual given channels
            </summary>
            <param name="r">red</param>
            <param name="g">green</param>
            <param name="b">blue</param>
            <param name="a">alpha/translucency</param>
        </member>
        <member name="M:CitizenFX.Core.Color.#ctor(System.UInt32)">
            <summary>
            Create a color with an unsinged integer
            </summary>
            <param name="argb">argb values</param>
        </member>
        <member name="M:CitizenFX.Core.Color.#ctor(System.Int32)">
            <summary>
            Create a color with a signed integer
            </summary>
            <param name="argb">argb values</param>
        </member>
        <member name="P:CitizenFX.Core.Color.Item(System.Int32)">
            <summary>
            Get or set the channel at the specified index
            </summary>
            <remarks>Does <paramref name="index"/> % 4 internally as it's faster than a boundary check</remarks>
            <param name="index">channel to get the value for</param>
            <returns></returns>
        </member>
        <member name="P:CitizenFX.Core.Color.Item(System.UInt32)">
            <summary>
            Get or set the channel at the specified index
            </summary>
            <remarks>Does <paramref name="index"/> % 4 internally as it's faster than a boundary check</remarks>
            <param name="index">channel to get the value for</param>
            <returns></returns>
        </member>
        <member name="T:CitizenFX.Core.Size2">
            <summary>
            Used for integer sizes with width and height values
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Size2.Width">
            <summary>
            Get or set the width of this element
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Size2.Height">
            <summary>
            Get or set the height of this element
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Size2.#ctor(System.Int32)">
            <summary>
            Create a new Size2 with both Width and Height set to <paramref name="value"/>
            </summary>
            <param name="value">value for both width and height</param>
        </member>
        <member name="M:CitizenFX.Core.Size2.#ctor(System.Int32,System.Int32)">
            <summary>
            Create a new Size2 with specified width and height values
            </summary>
            <param name="width">width of the Size2</param>
            <param name="height">height of the Size2</param>
        </member>
        <member name="P:CitizenFX.Core.Size2.Item(System.Int32)">
            <summary>
            Get or set the value at the specified index.
            </summary>
            <remarks>Does <paramref name="index"/> % 2 internally as it's faster than a boundary check</remarks>
            <param name="index">0 for Width and 1 for Height.</param>
            <returns>value in either component</returns>
        </member>
        <member name="P:CitizenFX.Core.Size2.Item(System.UInt32)">
            <summary>
            Get or set the value at the specified index.
            </summary>
            <remarks>Does <paramref name="index"/> % 2 internally as it's faster than a boundary check</remarks>
            <param name="index">0 for Width and 1 for Height.</param>
            <returns>value in either component</returns>
        </member>
        <member name="M:CitizenFX.Core.Native.CustomNativeInvoker.CustomInvocation.PushPinAndInvoke">
            <summary>
            Keeps adding arguments onto the argument stack, will recurse when arguments need to be fixed.
            This approach is 10x faster than the v1 approach in combination with CString
            </summary>
        </member>
        <member name="M:CitizenFX.Core.Native.CustomNativeInvoker.NativeStringResultSanitization(System.UInt64,CitizenFX.Core.Native.Argument[],System.UInt64*,System.Int32,System.UInt64*)">
            <summary>
            Sanitization for string result types
            Loops through all values given by the ScRT and deny any that equals the result value which isn't of the string type
            </summary>
            <returns>Result from <see cref="M:CitizenFX.Core.ScriptContext.GetResult(System.Type,System.UInt64*,System.UInt64)"/> or null if sanitized</returns>
        </member>
        <member name="M:CitizenFX.Core.Native.MemoryAccess.Offset(System.IntPtr,System.Int32)">
            <remarks>
            Purely used to circumvent <see cref="T:System.IntPtr"/> operator+'s <see langword="internal"/> accessibility in game libraries
            TODO: make mscorlib IntPtr operator+ public and [SecurityCritical]
            </remarks>
        </member>
        <member name="P:CitizenFX.Core.UnmanagedTypeInfo`1.Size">
            <summary>
            Get unmanaged type size, this will actually request the size from native, then we cache it
            </summary>
            <returns>unmanaged size of T</returns>
        </member>
        <member name="M:CitizenFX.Core.OutString.op_Implicit(CitizenFX.Core.OutString@)~System.String">
            <summary>
            A managed string that holds a conerted copy of the unmanaged ANSI string. If ptr is null, the method returns a null string.
            </summary>
            <param name="str"></param>
        </member>
        <member name="M:CitizenFX.Core.OutString.op_Explicit(CitizenFX.Core.OutString@)~System.Byte[]">
            <summary>
            A managed byte[] that holds a copy of the unmanaged ANSI string. If ptr is null, the method returns a null string.
            </summary>
            <param name="str"></param>
        </member>
        <member name="M:CitizenFX.Core.OutString.ToCString">
            <summary>
            Creates a null terminated C-string out of the returned string
            </summary>
            <returns>null terminated C-string</returns>
        </member>
        <member name="M:CitizenFX.Core.OutString.SubString(System.UInt32,System.UInt32)">
            <summary>
            Retrieves a substring from this string. Starting at the specified <paramref name="startIndex"/> in strides of <see cref="T:System.Byte"/>
            </summary>
            <param name="startIndex">Character index to start the new string with</param>
            <param name="length">Amount of characters requested, after <paramref name="startIndex"/></param>
            <returns><see cref="T:System.String"/> starting from <paramref name="startIndex"/>, <see cref="F:System.String.Empty"/> if we passed the end, or <see langword="null"/></returns>
        </member>
        <member name="M:CitizenFX.Core.OutString.SubCString(System.UInt32,System.UInt32)">
            <summary>
            Retrieves a substring from this string. Starting at the specified <paramref name="startIndex"/> in strides of <see cref="T:System.Byte"/>
            </summary>
            <param name="startIndex">Character index to start the new string with</param>
            <param name="length">Amount of characters requested, after <paramref name="startIndex"/></param>
            <returns><see cref="T:CitizenFX.Core.CString"/> starting from <paramref name="startIndex"/>, <see cref="P:CitizenFX.Core.CString.Empty"/> if we passed the end, or <see langword="null"/></returns>
        </member>
        <member name="M:CitizenFX.Core.Profiler.EnterScope(System.Func{System.String})">
            <param name="scopeName">Will only be evaluated when we're profiling, good for formatting strings</param>
        </member>
        <member name="T:CitizenFX.Core.ProfilerScope">
            <remarks>Mind you: using this over Profiler.EnterScope()/Profiler.ExitScope() has a slightly higher performance impact.</remarks>
        </member>
        <member name="M:CitizenFX.Core.ProfilerScope.#ctor(System.String)">
            <remarks>Mind you: using this over Profiler.EnterScope()/Profiler.ExitScope() has a slightly higher performance impact.</remarks>
        </member>
        <member name="M:CitizenFX.Core.ProfilerScope.#ctor(System.Func{System.String})">
            <param name="name">Will only be evaluated when we're profiling, good for formatting strings</param>
            <remarks>Mind you: using this over Profiler.EnterScope()/Profiler.ExitScope() has a slightly higher performance impact.</remarks>
        </member>
        <member name="T:CitizenFX.Core.Resource">
            <summary>
            Reference to a resource that may or may not exist, can be used as a shortcut to call its exports
            <see cref="P:CitizenFX.Core.Resource.Current"/> contains the information of this script's resource
            </summary>
            <remarks>Doesn't guarantee the resource is initialized, active, or has an exports ready.</remarks>
        </member>
        <member name="F:CitizenFX.Core.Resource.IsServer">
            <summary>
            Can be used to compile against server or client code
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Resource.Current">
            <summary>
            Get this script's resource information
            </summary>
            <remarks>Don't use its <see cref="P:CitizenFX.Core.Resource.Exports"/> as that'll be quite slow, unless needed, e.g.: set in another assembly.</remarks>
        </member>
        <member name="P:CitizenFX.Core.Resource.Name">
            <summary>
            The resource's name
            </summary>
        </member>
        <member name="P:CitizenFX.Core.Resource.Exports">
            <summary>
            Can be used to call Export functions on this resource
            </summary>
            <remarks>Can fail just like calling exports normally as this type doesn't guarantee the resource exists,
            is initialized, active, or even has an export with that name.</remarks>
        </member>
        <member name="M:CitizenFX.Core.Resource.#ctor(System.String)">
            <summary>
            Creates a reference to a resource that may or may not exist
            </summary>
            <param name="name">name of the resource we want to reference</param>
            <remarks>Doesn't guarantee the resource is initialized, active, or has an exports ready.</remarks>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.AddStateBagChangeHandler(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Delegate)">
            <summary>
            Adds a handler for changes to a state bag.
            
            The function called expects to match the following signature:
            
            ```ts
            function StateBagChangeHandler(bagName: string, key: string, value: any, reserved: number, replicated: boolean);
            ```
            
            *   **bagName**: The internal bag ID for the state bag which changed. This is usually `player:Source`, `entity:NetID`
                or `localEntity:Handle`.
            *   **key**: The changed key.
            *   **value**: The new value stored at key. The old value is still stored in the state bag at the time this callback executes.
            *   **reserved**: Currently unused.
            *   **replicated**: Whether the set is meant to be replicated.
            
            At this time, the change handler can't opt to reject changes.
            
            If bagName refers to an entity, use [GET_ENTITY_FROM_STATE_BAG_NAME](?\_0x4BDF1868) to get the entity handle
            If bagName refers to a player, use [GET_PLAYER_FROM_STATE_BAG_NAME](?\_0xA56135E0) to get the player handle
            </summary>
            <param name="keyFilter">
            The key to check for, or null for no filter.
            </param>
            <param name="bagFilter">
            The bag ID to check for such as `entity:65535`, or null for no filter.
            </param>
            <param name="handler">
            The handler function.
            </param>
            <returns>
            A cookie to remove the change handler.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.CancelEvent">
            <summary>
            Cancels the currently executing event.
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.DeleteFunctionReference(CitizenFX.Core.CString)">
            <summary>
            DELETE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.DeleteResourceKvp(CitizenFX.Core.CString)">
            <summary>
            DELETE_RESOURCE_KVP
            </summary>
            <param name="key">
            The key to delete
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.DuplicateFunctionReference(CitizenFX.Core.CString)">
            <summary>
            DUPLICATE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.EndFindKvp(System.Int32)">
            <summary>
            END_FIND_KVP
            </summary>
            <param name="handle">
            The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
            </param>
            <returns>
            None.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.EnsureEntityStateBag(System.Int32)">
            <summary>
            Internal function for ensuring an entity has a state bag.
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.ExecuteCommand(CitizenFX.Core.CString)">
            <summary>
            EXECUTE_COMMAND
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.FindKvp(System.Int32)">
            <summary>
            FIND_KVP
            </summary>
            <param name="handle">
            The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
            </param>
            <returns>
            None.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetConvar(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Can be used to get a console variable of type `char*`, for example a string.
            </summary>
            <param name="varName">
            The console variable to look up.
            </param>
            <param name="default_">
            The default value to set if none is found.
            </param>
            <returns>
            Returns the convar value if it can be found, otherwise it returns the assigned `default`.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetConvarInt(CitizenFX.Core.CString,System.Int32)">
            <summary>
            Can be used to get a console variable casted back to `int` (an integer value).
            </summary>
            <param name="varName">
            The console variable to look up.
            </param>
            <param name="default_">
            The default value to set if none is found (variable not set using [SET_CONVAR](#\_0x341B16D2), or not accessible).
            </param>
            <returns>
            Returns the convar value if it can be found, otherwise it returns the assigned `default`.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetCurrentResourceName">
            <summary>
            Returns the name of the currently executing resource.
            </summary>
            <returns>
            The name of the resource.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetEntityFromStateBagName(CitizenFX.Core.CString)">
            <summary>
            Returns the entity handle for the specified state bag name. For use with [ADD_STATE_BAG_CHANGE_HANDLER](?\_0x5BA35AAF).
            </summary>
            <param name="bagName">
            An internal state bag ID from the argument to a state bag change handler.
            </param>
            <returns>
            The entity handle or 0 if the state bag name did not refer to an entity, or the entity does not exist.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetGameBuildNumber">
            <summary>
            Returns the internal build number of the current game being executed.
            
            Possible values:
            
            *   FiveM
                *   1604
                *   2060
                *   2189
                *   2372
                *   2545
                *   2612
                *   2699
                *   2802
                *   2944
                *   3095
            *   RedM
                *   1311
                *   1355
                *   1436
                *   1491
            *   LibertyM
                *   43
            *   FXServer
                *   0
            </summary>
            <returns>
            The build number, or **0** if no build number is known.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetGameName">
            <summary>
            Returns the current game being executed.
            
            Possible values:
            
            | Return value | Meaning                        |
            | ------------ | ------------------------------ |
            | `fxserver`   | Server-side code ('Duplicity') |
            | `fivem`      | FiveM for GTA V                |
            | `libertym`   | LibertyM for GTA IV            |
            | `redm`       | RedM for Red Dead Redemption 2 |
            </summary>
            <returns>
            The game the script environment is running in.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetInstanceId">
            <summary>
            GET_INSTANCE_ID
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetInvokingResource">
            <summary>
            GET_INVOKING_RESOURCE
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetNumResourceMetadata(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/resources/manifest/)
            </summary>
            <param name="resourceName">
            The resource name.
            </param>
            <param name="metadataKey">
            The key to look up in the resource manifest.
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetNumResources">
            <summary>
            GET_NUM_RESOURCES
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetPlayerFromStateBagName(CitizenFX.Core.CString)">
            <summary>
            On the server this will return the players source, on the client it will return the player handle.
            </summary>
            <param name="bagName">
            An internal state bag ID from the argument to a state bag change handler.
            </param>
            <returns>
            The player handle or 0 if the state bag name did not refer to a player, or the player does not exist.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetPlayerMeleeWeaponDamageModifier(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER](#\_0x4A3DC7ECCC321032).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            Returns player melee weapon damage modifier value.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetPlayerWeaponDamageModifier(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DAMAGE_MODIFIER](#\_0xCE07B9F7817AADA3).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            The value of player weapon damage modifier.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetPlayerWeaponDefenseModifier(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DEFENSE_MODIFIER](#\_0x2D83BC011CA14A3C).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            The value of player weapon defense modifier.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetPlayerWeaponDefenseModifier_2(CitizenFX.Core.CString)">
            <summary>
            A getter for [\_SET_PLAYER_WEAPON_DEFENSE_MODIFIER\_2](#\_0xBCFDE9EDE4CF27DC).
            </summary>
            <param name="playerId">
            The player index.
            </param>
            <returns>
            The value of player weapon defense modifier 2.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetRegisteredCommands">
            <summary>
            Returns all commands that are registered in the command system.
            The data returned adheres to the following layout:
            
            ```
            [
            {
            "name": "cmdlist"
            },
            {
            "name": "command1"
            }
            ]
            ```
            </summary>
            <returns>
            An object containing registered commands.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetResourceByFindIndex(System.Int32)">
            <summary>
            GET_RESOURCE_BY_FIND_INDEX
            </summary>
            <param name="findIndex">
            The index of the resource (starting at 0)
            </param>
            <returns>
            The resource name as a `string`
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetResourceKvpFloat(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938).
            </summary>
            <param name="key">
            The key to fetch
            </param>
            <returns>
            The floating-point value stored under the specified key, or 0.0 if not found.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetResourceKvpInt(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8).
            </summary>
            <param name="key">
            The key to fetch
            </param>
            <returns>
            The integer value stored under the specified key, or 0 if not found.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetResourceKvpString(CitizenFX.Core.CString)">
            <summary>
            A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B).
            </summary>
            <param name="key">
            The key to fetch
            </param>
            <returns>
            The string value stored under the specified key, or nil/null if not found.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetResourceMetadata(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            Gets the metadata value at a specified key/index from a resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/resources/manifest/)
            </summary>
            <param name="resourceName">
            The resource name.
            </param>
            <param name="metadataKey">
            The key in the resource manifest.
            </param>
            <param name="index">
            The value index, in a range from \[0..GET_NUM_RESOURCE_METDATA-1].
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetResourceState(CitizenFX.Core.CString)">
            <summary>
            Returns the current state of the specified resource.
            </summary>
            <param name="resourceName">
            The name of the resource.
            </param>
            <returns>
            The resource state. One of `"missing", "started", "starting", "stopped", "stopping", "uninitialized" or "unknown"`.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetStateBagValue(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Returns the value of a state bag key.
            </summary>
            <returns>
            Value.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetVehicleHandbrake(System.Int32)">
            <summary>
            GET_VEHICLE_HANDBRAKE
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.GetVehicleSteeringAngle(System.Int32)">
            <summary>
            GET_VEHICLE_STEERING_ANGLE
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.InvokeFunctionReference(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32,System.Int32@)">
            <summary>
            INVOKE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.IsAceAllowed(CitizenFX.Core.CString)">
            <summary>
            IS_ACE_ALLOWED
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.IsDuplicityVersion">
            <summary>
            Gets whether or not this is the CitizenFX server.
            </summary>
            <returns>
            A boolean value.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.IsEntityPositionFrozen(System.Int32)">
            <summary>
            A getter for [FREEZE_ENTITY_POSITION](#\_0x428CA6DBD1094446).
            </summary>
            <param name="entity">
            The entity to check for
            </param>
            <returns>
            Boolean stating if it is frozen or not.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.IsPrincipalAceAllowed(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            IS_PRINCIPAL_ACE_ALLOWED
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.IsVehicleEngineStarting(System.Int32)">
            <summary>
            IS_VEHICLE_ENGINE_STARTING
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.LoadResourceFile(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            Reads the contents of a text file in a specified resource.
            If executed on the client, this file has to be included in `files` in the resource manifest.
            Example: `local data = LoadResourceFile("devtools", "data.json")`
            </summary>
            <param name="resourceName">
            The resource name.
            </param>
            <param name="fileName">
            The file in the resource.
            </param>
            <returns>
            The file contents
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.NetworkGetEntityOwner(System.Int32)">
            <summary>
            Returns the owner ID of the specified entity.
            </summary>
            <param name="entity">
            The entity to get the owner for.
            </param>
            <returns>
            On the server, the server ID of the entity owner. On the client, returns the player/slot ID of the entity owner.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.ProfilerEnterScope(CitizenFX.Core.CString)">
            <summary>
            Scope entry for profiler.
            </summary>
            <param name="scopeName">
            Scope name.
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.ProfilerExitScope">
            <summary>
            Scope exit for profiler.
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.ProfilerIsRecording">
            <summary>
            Returns true if the profiler is active.
            </summary>
            <returns>
            True or false.
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.RegisterCommand(CitizenFX.Core.CString,System.Delegate,System.Boolean)">
            <summary>
            Registered commands can be executed by entering them in the client console (this works for client side and server side registered commands). Or by entering them in the server console/through an RCON client (only works for server side registered commands). Or if you use a supported chat resource, like the default one provided in the cfx-server-data repository, then you can enter the command in chat by prefixing it with a `/`.
            
            Commands registered using this function can also be executed by resources, using the [`ExecuteCommand` native](#\_0x561C060B).
            
            The restricted bool is not used on the client side. Permissions can only be checked on the server side, so if you want to limit your command with an ace permission automatically, make it a server command (by registering it in a server script).
            
            **Example result**:
            
            ![](https://i.imgur.com/TaCnG09.png)
            </summary>
            <param name="commandName">
            The command you want to register.
            </param>
            <param name="handler">
            A handler function that gets called whenever the command is executed.
            </param>
            <param name="restricted">
            If this is a server command and you set this to true, then players will need the command.yourCommandName ace permission to execute this command.
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.RegisterResourceAsEventHandler(CitizenFX.Core.CString)">
            <summary>
            An internal function which allows the current resource's HLL script runtimes to receive state for the specified event.
            </summary>
            <param name="eventName">
            An event name, or "\*" to disable HLL event filtering for this resource.
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.RemoveStateBagChangeHandler(System.Int32)">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Removes a handler for changes to a state bag.
            </summary>
            <param name="cookie">
            The cookie.
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.SetResourceKvp(CitizenFX.Core.CString,CitizenFX.Core.CString)">
            <summary>
            A setter for [GET_RESOURCE_KVP_STRING](#\_0x5240DA5A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.SetResourceKvpFloat(CitizenFX.Core.CString,System.Single)">
            <summary>
            A setter for [GET_RESOURCE_KVP_FLOAT](#\_0x35BDCEEA).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.SetResourceKvpInt(CitizenFX.Core.CString,System.Int32)">
            <summary>
            A setter for [GET_RESOURCE_KVP_INT](#\_0x557B586A).
            </summary>
            <param name="key">
            The key to set
            </param>
            <param name="_value">
            The value to write
            </param>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.SetStateBagValue(CitizenFX.Core.CString,CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32,System.Boolean)">
            <summary>
            Internal function for setting a state bag value.
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.StartFindKvp(CitizenFX.Core.CString)">
            <summary>
            START_FIND_KVP
            </summary>
            <param name="prefix">
            A prefix match
            </param>
            <returns>
            A KVP find handle to use with [FIND_KVP](#\_0xBD7BEBC5) and close with [END_FIND_KVP](#\_0xB3210203)
            </returns>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.TriggerEventInternal(CitizenFX.Core.CString,CitizenFX.Core.CString,System.Int32)">
            <summary>
            The backing function for TriggerEvent.
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Native.Natives.WasEventCanceled">
            <summary>
            Returns whether or not the currently executing event was canceled.
            </summary>
            <returns>
            A boolean.
            </returns>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.ADD_STATE_BAG_CHANGE_HANDLER">
            <summary>
            Adds a handler for changes to a state bag.
            
            The function called expects to match the following signature:
            
            ```ts
            function StateBagChangeHandler(bagName: string, key: string, value: any, reserved: number, replicated: boolean);
            ```
            
            *   **bagName**: The internal bag ID for the state bag which changed. This is usually `player:Source`, `entity:NetID`
                or `localEntity:Handle`.
            *   **key**: The changed key.
            *   **value**: The new value stored at key. The old value is still stored in the state bag at the time this callback executes.
            *   **reserved**: Currently unused.
            *   **replicated**: Whether the set is meant to be replicated.
            
            At this time, the change handler can't opt to reject changes.
            
            If bagName refers to an entity, use [GET_ENTITY_FROM_STATE_BAG_NAME](?\_0x4BDF1868) to get the entity handle
            If bagName refers to a player, use [GET_PLAYER_FROM_STATE_BAG_NAME](?\_0xA56135E0) to get the player handle
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.CANCEL_EVENT">
            <summary>
            Cancels the currently executing event.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.DELETE_FUNCTION_REFERENCE">
            <summary>
            DELETE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.DELETE_RESOURCE_KVP">
            <summary>
            DELETE_RESOURCE_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.DUPLICATE_FUNCTION_REFERENCE">
            <summary>
            DUPLICATE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.END_FIND_KVP">
            <summary>
            END_FIND_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.ENSURE_ENTITY_STATE_BAG">
            <summary>
            Internal function for ensuring an entity has a state bag.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.EXECUTE_COMMAND">
            <summary>
            EXECUTE_COMMAND
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.FIND_KVP">
            <summary>
            FIND_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_CONVAR">
            <summary>
            Can be used to get a console variable of type `char*`, for example a string.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_CONVAR_INT">
            <summary>
            Can be used to get a console variable casted back to `int` (an integer value).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_CURRENT_RESOURCE_NAME">
            <summary>
            Returns the name of the currently executing resource.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_ENTITY_FROM_STATE_BAG_NAME">
            <summary>
            Returns the entity handle for the specified state bag name. For use with [ADD_STATE_BAG_CHANGE_HANDLER](?\_0x5BA35AAF).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_GAME_BUILD_NUMBER">
            <summary>
            Returns the internal build number of the current game being executed.
            
            Possible values:
            
            *   FiveM
                *   1604
                *   2060
                *   2189
                *   2372
                *   2545
                *   2612
                *   2699
                *   2802
                *   2944
                *   3095
            *   RedM
                *   1311
                *   1355
                *   1436
                *   1491
            *   LibertyM
                *   43
            *   FXServer
                *   0
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_GAME_NAME">
            <summary>
            Returns the current game being executed.
            
            Possible values:
            
            | Return value | Meaning                        |
            | ------------ | ------------------------------ |
            | `fxserver`   | Server-side code ('Duplicity') |
            | `fivem`      | FiveM for GTA V                |
            | `libertym`   | LibertyM for GTA IV            |
            | `redm`       | RedM for Red Dead Redemption 2 |
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_INSTANCE_ID">
            <summary>
            GET_INSTANCE_ID
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_INVOKING_RESOURCE">
            <summary>
            GET_INVOKING_RESOURCE
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_NUM_RESOURCE_METADATA">
            <summary>
            Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/resources/manifest/)
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_NUM_RESOURCES">
            <summary>
            GET_NUM_RESOURCES
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_PLAYER_FROM_STATE_BAG_NAME">
            <summary>
            On the server this will return the players source, on the client it will return the player handle.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER">
            <summary>
            A getter for [SET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER](#\_0x4A3DC7ECCC321032).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_PLAYER_WEAPON_DAMAGE_MODIFIER">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DAMAGE_MODIFIER](#\_0xCE07B9F7817AADA3).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_PLAYER_WEAPON_DEFENSE_MODIFIER">
            <summary>
            A getter for [SET_PLAYER_WEAPON_DEFENSE_MODIFIER](#\_0x2D83BC011CA14A3C).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_PLAYER_WEAPON_DEFENSE_MODIFIER_2">
            <summary>
            A getter for [\_SET_PLAYER_WEAPON_DEFENSE_MODIFIER\_2](#\_0xBCFDE9EDE4CF27DC).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_REGISTERED_COMMANDS">
            <summary>
            Returns all commands that are registered in the command system.
            The data returned adheres to the following layout:
            
            ```
            [
            {
            "name": "cmdlist"
            },
            {
            "name": "command1"
            }
            ]
            ```
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_RESOURCE_BY_FIND_INDEX">
            <summary>
            GET_RESOURCE_BY_FIND_INDEX
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_RESOURCE_KVP_FLOAT">
            <summary>
            A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_RESOURCE_KVP_INT">
            <summary>
            A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_RESOURCE_KVP_STRING">
            <summary>
            A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_RESOURCE_METADATA">
            <summary>
            Gets the metadata value at a specified key/index from a resource's manifest.
            See also: [Resource manifest](https://docs.fivem.net/resources/manifest/)
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_RESOURCE_STATE">
            <summary>
            Returns the current state of the specified resource.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_STATE_BAG_VALUE">
            <summary>
            Returns the value of a state bag key.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_VEHICLE_HANDBRAKE">
            <summary>
            GET_VEHICLE_HANDBRAKE
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.GET_VEHICLE_STEERING_ANGLE">
            <summary>
            GET_VEHICLE_STEERING_ANGLE
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.INVOKE_FUNCTION_REFERENCE">
            <summary>
            INVOKE_FUNCTION_REFERENCE
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.IS_ACE_ALLOWED">
            <summary>
            IS_ACE_ALLOWED
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.IS_DUPLICITY_VERSION">
            <summary>
            Gets whether or not this is the CitizenFX server.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.IS_ENTITY_POSITION_FROZEN">
            <summary>
            A getter for [FREEZE_ENTITY_POSITION](#\_0x428CA6DBD1094446).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.IS_PRINCIPAL_ACE_ALLOWED">
            <summary>
            IS_PRINCIPAL_ACE_ALLOWED
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.IS_VEHICLE_ENGINE_STARTING">
            <summary>
            IS_VEHICLE_ENGINE_STARTING
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.LOAD_RESOURCE_FILE">
            <summary>
            Reads the contents of a text file in a specified resource.
            If executed on the client, this file has to be included in `files` in the resource manifest.
            Example: `local data = LoadResourceFile("devtools", "data.json")`
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.NETWORK_GET_ENTITY_OWNER">
            <summary>
            Returns the owner ID of the specified entity.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.PROFILER_ENTER_SCOPE">
            <summary>
            Scope entry for profiler.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.PROFILER_EXIT_SCOPE">
            <summary>
            Scope exit for profiler.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.PROFILER_IS_RECORDING">
            <summary>
            Returns true if the profiler is active.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.REGISTER_COMMAND">
            <summary>
            Registered commands can be executed by entering them in the client console (this works for client side and server side registered commands). Or by entering them in the server console/through an RCON client (only works for server side registered commands). Or if you use a supported chat resource, like the default one provided in the cfx-server-data repository, then you can enter the command in chat by prefixing it with a `/`.
            
            Commands registered using this function can also be executed by resources, using the [`ExecuteCommand` native](#\_0x561C060B).
            
            The restricted bool is not used on the client side. Permissions can only be checked on the server side, so if you want to limit your command with an ace permission automatically, make it a server command (by registering it in a server script).
            
            **Example result**:
            
            ![](https://i.imgur.com/TaCnG09.png)
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.REGISTER_RESOURCE_AS_EVENT_HANDLER">
            <summary>
            An internal function which allows the current resource's HLL script runtimes to receive state for the specified event.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.REMOVE_STATE_BAG_CHANGE_HANDLER">
            <summary>
            **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
            
            Removes a handler for changes to a state bag.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.SET_RESOURCE_KVP">
            <summary>
            A setter for [GET_RESOURCE_KVP_STRING](#\_0x5240DA5A).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.SET_RESOURCE_KVP_FLOAT">
            <summary>
            A setter for [GET_RESOURCE_KVP_FLOAT](#\_0x35BDCEEA).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.SET_RESOURCE_KVP_INT">
            <summary>
            A setter for [GET_RESOURCE_KVP_INT](#\_0x557B586A).
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.SET_STATE_BAG_VALUE">
            <summary>
            Internal function for setting a state bag value.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.START_FIND_KVP">
            <summary>
            START_FIND_KVP
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.TRIGGER_EVENT_INTERNAL">
            <summary>
            The backing function for TriggerEvent.
            </summary>
        </member>
        <member name="F:CitizenFX.Shared.Native.Hash.WAS_EVENT_CANCELED">
            <summary>
            Returns whether or not the currently executing event was canceled.
            </summary>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.Position">
            <summary>
            Gets or sets the position of this <see cref="T:CitizenFX.Shared.IEntity"/>.
            </summary>
            <value>
            The position in world space.
            </value>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.Rotation">
            <summary>
            Gets or sets the rotation of this <see cref="T:CitizenFX.Shared.IEntity"/>.
            </summary>
            <value>
            The yaw, pitch, roll rotation values.
            </value>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.Heading">
            <summary>
            Gets or sets the heading of this <see cref="T:CitizenFX.Shared.IEntity"/>.
            </summary>
            <value>
            The heading in degrees.
            </value>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.IsPositionFrozen">
            <summary>
            Sets a value indicating whether this <see cref="T:CitizenFX.Shared.IEntity"/> should be frozen.
            </summary>
            <value>
            <c>true</c> if this <see cref="T:CitizenFX.Shared.IEntity"/> position should be frozen; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.Velocity">
            <summary>
            Gets or sets the velocity of this <see cref="T:CitizenFX.Shared.IEntity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.RotationVelocity">
            <summary>
            Gets the rotation velocity of this <see cref="T:CitizenFX.Shared.IEntity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.Model">
            <summary>
            Gets the model of the this <see cref="T:CitizenFX.Shared.IEntity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.Owner">
            <summary>
            Gets the network owner of the this <see cref="T:CitizenFX.Shared.IEntity"/>.
            </summary>
            <returns>Returns the <see cref="T:CitizenFX.Shared.Player"/> of the network owner.
            Returns <c>null</c> if this <see cref="T:CitizenFX.Shared.IEntity"/> is in an unowned state.</returns>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.NetworkId">
            <summary>
            Gets the network ID of the this <see cref="T:CitizenFX.Shared.IEntity"/>.
            </summary>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.Type">
            <summary>
            Gets the type of this <see cref="T:CitizenFX.Shared.IEntity"/>.
            </summary>
            <returns>Returns 1 if this <see cref="T:CitizenFX.Shared.IEntity"/> is a Ped.
            Returns 2 if this <see cref="T:CitizenFX.Shared.IEntity"/> is a Vehicle.
            Returns 3 if this <see cref="T:CitizenFX.Shared.IEntity"/> is a Prop.</returns>
        </member>
        <member name="P:CitizenFX.Shared.IEntity.State">
            <summary>
            Gets the <see cref="T:CitizenFX.Core.StateBag"/> of this <see cref="T:CitizenFX.Shared.IEntity"/>
            </summary>
        </member>
        <member name="P:CitizenFX.Shared.Player.Handle">
            <summary>
            Gets the handle of this player
            </summary>
        </member>
        <member name="P:CitizenFX.Shared.Player.Name">
            <summary>
            Gets the name of this player
            </summary>
        </member>
        <member name="P:CitizenFX.Shared.Player.State">
            <summary>
            Access the <see cref="T:CitizenFX.Core.StateBag"/> of this player
            </summary>
        </member>
        <member name="M:CitizenFX.Shared.Player.GetCharacter">
            <summary>
            Gets the <see cref="T:CitizenFX.Shared.IPed"/> this player is controling, use Ped.Character on Ped for non-shared access.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.SkipLocalsInitAttribute">
            <summary>
            Used to indicate to the compiler that the <c>.locals init</c> flag should
            not be set in nested method headers when emitting to metadata.
            </summary>
            <remarks>
            This attribute is unsafe because it may reveal uninitialized memory to
            the application in certain instances (e.g., reading from uninitialized
            stackalloc'd memory). If applied to a method directly, the attribute
            applies to that method and all nested functions (lambdas, local
            functions) below it. If applied to a type or module, it applies to all
            methods nested inside. This attribute is intentionally not permitted on
            assemblies. Use at the module level instead to apply to multiple type
            declarations.
            </remarks>
        </member>
    </members>
</doc>
