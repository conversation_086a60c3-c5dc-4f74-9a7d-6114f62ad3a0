.color-0{color: #ffffff;}
.color-1{color: #ff4444;}
.color-2{color: #99cc00;}
.color-3{color: #ffbb33;}
.color-4{color: #0099cc;}
.color-5{color: #33b5e5;}
.color-6{color: #aa66cc;}
.color-8{color: #cc0000;}
.color-9{color: #cc0068;}

.gameColor-w{color: #ffffff;}
.gameColor-r{color: #ff4444;}
.gameColor-g{color: #99cc00;}
.gameColor-y{color: #ffbb33;}
.gameColor-b{color: #33b5e5;}

/* todo: more game colors */

* {
  font-family: 'Lato', sans-serif;
  margin: 0;
  padding: 0;
}

.no-grow {
  flex-grow: 0;
}

em {
  font-style: normal;
}

#app {
  font-family: 'Lato', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: white;
}

.chat-window {
  position: absolute;
  top: 1.5%;
  left: 0.8%;
  width: 38%;
  height: 22%;
  max-width: 1000px;
  background-color: rgba(52, 73, 94, 0.7);
  -webkit-animation-duration: 2s;
}


.chat-messages {
  position: relative;
  height: 95%;
  font-size: 1.8vh;
  margin: 1%;

  overflow-x: hidden;
  overflow-y: hidden;
}


.chat-input {
  font-size: 1.65vh;
  position: absolute;

  top: 23.8%;
  left: 0.8%;
  width: 38%;
  max-width: 1000px;
  box-sizing: border-box;
}

.chat-input > div.input {
  position: relative;
  display: flex;
  align-items: stretch;
  width: 100%;
  background-color: rgba(44, 62, 80, 1.0);
}

.chat-hide-state {
  text-transform: uppercase;
  margin-left: 0.05vw;
  font-size: 1.65vh;
}

.prefix {
  font-size: 1.8vh;
  /*position: absolute;
  top: 0%;*/
  height: 100%;
  vertical-align: middle;
  line-height: calc(1vh + 1vh + 1.85vh);
  padding-left: 0.5vh;
  text-transform: uppercase;
  font-weight: bold;
  display: inline-block;
}

textarea {
  font-size: 1.65vh;
  line-height: 1.85vh;
  display: block;
  box-sizing: content-box;
  padding: 1vh;
  padding-left: 0.5vh;
  color: white;
  border-width: 0;
  height: 3.15%;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  background-color: transparent;
}

textarea:focus, input:focus {
    outline: none;
}

.msg {
  margin-bottom: 0.28%;
}

.multiline {
  margin-left: 4%;
  text-indent: -1.2rem;
  white-space: pre-line;
}

.suggestions {
  list-style-type: none;
  padding: 0.5%;
  padding-left: 1.4%;
  font-size: 1.65vh;
  box-sizing: border-box;
  color: white;
  background-color: rgba(44, 62, 80, 1.0);
  width: 100%;
}

.help {
  color: #b0bbbd;
}

.disabled {
  color: #b0bbbd;
}

.suggestion {
  margin-bottom: 0.5%;
}

.hidden {
  opacity: 0;
}

.hidden.animated {
  transition: opacity 1s;
}